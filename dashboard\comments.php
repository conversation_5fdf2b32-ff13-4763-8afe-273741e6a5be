<?php
session_start();
require_once '../db/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// معالجة الموافقة على التعليق
if (isset($_POST['approve_comment'])) {
    $commentId = intval($_POST['comment_id']);
    $stmt = $pdo->prepare("UPDATE comments SET is_approved = 1 WHERE id = ?");
    if ($stmt->execute([$commentId])) {
        $success = 'تم الموافقة على التعليق بنجاح!';
    }
}

// معالجة حذف التعليق
if (isset($_POST['delete_comment'])) {
    $commentId = intval($_POST['comment_id']);
    $stmt = $pdo->prepare("DELETE FROM comments WHERE id = ?");
    if ($stmt->execute([$commentId])) {
        $success = 'تم حذف التعليق بنجاح!';
    }
}

// جلب التعليقات
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$comments = $pdo->prepare("
    SELECT c.*, u.full_name as user_name, d.title as document_title
    FROM comments c
    JOIN users u ON c.user_id = u.id
    JOIN documents d ON c.document_id = d.id
    ORDER BY c.created_at DESC
    LIMIT ? OFFSET ?
");
$comments->execute([$limit, $offset]);
$comments = $comments->fetchAll();

// حساب إجمالي التعليقات
$totalComments = $pdo->query("SELECT COUNT(*) FROM comments")->fetchColumn();
$totalPages = ceil($totalComments / $limit);

// إحصائيات التعليقات
$stats = [
    'total_comments' => $totalComments,
    'approved_comments' => $pdo->query("SELECT COUNT(*) FROM comments WHERE is_approved = 1")->fetchColumn(),
    'pending_comments' => $pdo->query("SELECT COUNT(*) FROM comments WHERE is_approved = 0")->fetchColumn(),
    'recent_comments' => $pdo->query("SELECT COUNT(*) FROM comments WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn()
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة التعليقات - لوحة التحكم</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: #fff;
        }

        .sidebar-menu i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            margin-right: 250px;
            padding: 2rem;
        }

        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .page-header h1 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
            font-size: 2rem;
        }

        .page-header p {
            margin: 0;
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9rem;
        }

        .comments-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-bottom: 1px solid #e1e8ed;
        }

        .comment-card {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e1e8ed;
            transition: background-color 0.3s ease;
        }

        .comment-card:hover {
            background: #f8f9fa;
        }

        .comment-card.pending {
            border-right: 4px solid #f39c12;
            background: #fff8e1;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .comment-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .user-info h4 {
            margin: 0 0 0.25rem 0;
            color: #2c3e50;
        }

        .user-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .comment-status {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-approved { background: #27ae60; color: white; }
        .status-pending { background: #f39c12; color: white; }

        .comment-content {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            line-height: 1.6;
            color: #2c3e50;
        }

        .comment-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
        }

        .document-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
        }

        .document-link:hover {
            text-decoration: underline;
        }

        .comment-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 2rem;
        }

        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #e1e8ed;
            border-radius: 5px;
            text-decoration: none;
            color: #2c3e50;
        }

        .pagination a:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination .current {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-right: 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .comment-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .comment-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-graduation-cap"></i> لوحة التحكم</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="documents.php"><i class="fas fa-file-alt"></i> إدارة الملفات</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subjects.php"><i class="fas fa-book"></i> إدارة المواد</a></li>
                <li><a href="comments.php" class="active"><i class="fas fa-comments"></i> التعليقات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../index.php"><i class="fas fa-arrow-right"></i> العودة للموقع</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-comments"></i> إدارة التعليقات</h1>
                <p>مراجعة والموافقة على تعليقات المستخدمين</p>
            </div>

            <!-- Alerts -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?= $success ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon" style="color: #3498db;"><i class="fas fa-comments"></i></div>
                    <div class="number"><?= number_format($stats['total_comments']) ?></div>
                    <div class="label">إجمالي التعليقات</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #27ae60;"><i class="fas fa-check-circle"></i></div>
                    <div class="number"><?= number_format($stats['approved_comments']) ?></div>
                    <div class="label">التعليقات المعتمدة</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #f39c12;"><i class="fas fa-clock"></i></div>
                    <div class="number"><?= number_format($stats['pending_comments']) ?></div>
                    <div class="label">في انتظار الموافقة</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #e74c3c;"><i class="fas fa-calendar-week"></i></div>
                    <div class="number"><?= number_format($stats['recent_comments']) ?></div>
                    <div class="label">هذا الأسبوع</div>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="comments-section">
                <div class="section-header">
                    <h3><i class="fas fa-list"></i> قائمة التعليقات (<?= number_format($totalComments) ?> تعليق)</h3>
                </div>

                <?php if (empty($comments)): ?>
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <i class="fas fa-comments" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h3>لا توجد تعليقات</h3>
                        <p>لم يتم العثور على أي تعليقات حتى الآن</p>
                    </div>
                <?php else: ?>
                    <?php foreach($comments as $comment): ?>
                        <div class="comment-card <?= !$comment['is_approved'] ? 'pending' : '' ?>">
                            <div class="comment-header">
                                <div class="comment-user">
                                    <div class="user-avatar">
                                        <?= strtoupper(substr($comment['user_name'], 0, 1)) ?>
                                    </div>
                                    <div class="user-info">
                                        <h4><?= htmlspecialchars($comment['user_name']) ?></h4>
                                        <p><?= date('Y-m-d H:i', strtotime($comment['created_at'])) ?></p>
                                    </div>
                                </div>
                                <div class="comment-status">
                                    <span class="status-badge status-<?= $comment['is_approved'] ? 'approved' : 'pending' ?>">
                                        <?= $comment['is_approved'] ? 'معتمد' : 'في الانتظار' ?>
                                    </span>
                                    <div class="comment-actions">
                                        <?php if (!$comment['is_approved']): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="comment_id" value="<?= $comment['id'] ?>">
                                                <button type="submit" name="approve_comment" class="btn btn-success">
                                                    <i class="fas fa-check"></i> موافقة
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="comment_id" value="<?= $comment['id'] ?>">
                                            <button type="submit" name="delete_comment" class="btn btn-danger" 
                                                    onclick="return confirm('هل أنت متأكد من حذف هذا التعليق؟')">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="comment-content">
                                <?= nl2br(htmlspecialchars($comment['content'])) ?>
                            </div>

                            <div class="comment-meta">
                                <span>
                                    تعليق على: 
                                    <a href="../view_document.php?id=<?= $comment['document_id'] ?>" class="document-link" target="_blank">
                                        <?= htmlspecialchars($comment['document_title']) ?>
                                    </a>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?= $page - 1 ?>">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?= $i ?></span>
                                <?php else: ?>
                                    <a href="?page=<?= $i ?>"><?= $i ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?= $page + 1 ?>">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
