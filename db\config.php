<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'school_platform');
define('DB_USER', 'root');
define('DB_PASS', '');

try {
    // إنشاء اتصال PDO
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    );
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// دالة للتحقق من صحة البريد الإلكتروني
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// دالة لتشفير كلمة المرور
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// دالة للتحقق من كلمة المرور
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// دالة للتحقق من صلاحيات المدير
function isAdmin() {
    return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
}

// دالة للتحقق من صلاحيات المعلم
function isTeacher() {
    return isset($_SESSION['role']) && ($_SESSION['role'] === 'teacher' || $_SESSION['role'] === 'admin');
}

// دالة لإعادة التوجيه
function redirect($url) {
    header("Location: $url");
    exit();
}

// دالة لإظهار الرسائل
function setMessage($message, $type = 'info') {
    $_SESSION['message'] = $message;
    $_SESSION['message_type'] = $type;
}

function getMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        $type = $_SESSION['message_type'] ?? 'info';
        unset($_SESSION['message'], $_SESSION['message_type']);
        return ['message' => $message, 'type' => $type];
    }
    return null;
}

// دالة لرفع الملفات
function uploadFile($file, $uploadDir = 'uploads/') {
    $allowedTypes = ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png'];
    $maxSize = 10 * 1024 * 1024; // 10MB
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'خطأ في رفع الملف'];
    }
    
    if ($file['size'] > $maxSize) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($fileExtension, $allowedTypes)) {
        return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
    }
    
    $fileName = uniqid() . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadDir . $fileName;
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $filePath)) {
        return ['success' => true, 'filename' => $fileName, 'path' => $filePath];
    } else {
        return ['success' => false, 'message' => 'فشل في حفظ الملف'];
    }
}

// دالة لحذف الملف
function deleteFile($filePath) {
    if (file_exists($filePath)) {
        return unlink($filePath);
    }
    return false;
}

// دالة لتنسيق التاريخ
function formatDate($date, $format = 'Y-m-d H:i') {
    return date($format, strtotime($date));
}

// دالة لتنسيق حجم الملف
function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

// دالة للحصول على معلومات المستخدم
function getUserInfo($userId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    return $stmt->fetch();
}

// دالة للحصول على المستويات
function getLevels() {
    global $pdo;
    return $pdo->query("SELECT * FROM levels ORDER BY id")->fetchAll();
}

// دالة للحصول على المواد حسب المستوى
function getSubjectsByLevel($levelId) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM subjects WHERE level_id = ? ORDER BY name");
    $stmt->execute([$levelId]);
    return $stmt->fetchAll();
}

// دالة للحصول على الوثائق حسب المادة
function getDocumentsBySubject($subjectId, $type = null) {
    global $pdo;
    $sql = "SELECT * FROM documents WHERE subject_id = ?";
    $params = [$subjectId];
    
    if ($type) {
        $sql .= " AND type = ?";
        $params[] = $type;
    }
    
    $sql .= " ORDER BY upload_date DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// دالة للبحث في الوثائق
function searchDocuments($query, $levelId = null, $subjectId = null, $type = null) {
    global $pdo;

    $sql = "SELECT d.*, s.name as subject_name, l.name as level_name
            FROM documents d
            JOIN subjects s ON d.subject_id = s.id
            JOIN levels l ON s.level_id = l.id
            WHERE (d.title LIKE ? OR d.description LIKE ?)";

    $params = ["%$query%", "%$query%"];

    if ($levelId) {
        $sql .= " AND l.id = ?";
        $params[] = $levelId;
    }

    if ($subjectId) {
        $sql .= " AND s.id = ?";
        $params[] = $subjectId;
    }

    if ($type) {
        $sql .= " AND d.type = ?";
        $params[] = $type;
    }

    $sql .= " ORDER BY d.upload_date DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// دالة لجلب السنوات الدراسية حسب المستوى
function getExamYears($levelId) {
    global $pdo;

    $stmt = $pdo->prepare("SELECT * FROM exam_years WHERE level_id = ? AND is_active = 1 ORDER BY year DESC");
    $stmt->execute([$levelId]);
    return $stmt->fetchAll();
}

// دالة لجلب الوثائق حسب السنة والمادة
function getDocumentsByYearAndSubject($subjectId, $year = null, $type = null, $session = null) {
    global $pdo;

    $sql = "SELECT d.*, s.name as subject_name, l.name as level_name
            FROM documents d
            JOIN subjects s ON d.subject_id = s.id
            JOIN levels l ON s.level_id = l.id
            WHERE d.subject_id = ?";

    $params = [$subjectId];

    if ($year) {
        $sql .= " AND d.exam_year = ?";
        $params[] = $year;
    }

    if ($type) {
        $sql .= " AND d.type = ?";
        $params[] = $type;
    }

    if ($session) {
        $sql .= " AND d.exam_session = ?";
        $params[] = $session;
    }

    $sql .= " ORDER BY d.exam_year DESC, d.upload_date DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// دالة لجلب إحصائيات الشهادات
function getExamStatistics($levelId) {
    global $pdo;

    $stmt = $pdo->prepare("
        SELECT
            COUNT(DISTINCT d.exam_year) as total_years,
            COUNT(CASE WHEN d.type = 'موضوع شهادة' THEN 1 END) as total_subjects,
            COUNT(CASE WHEN d.type = 'حل شهادة' THEN 1 END) as total_solutions,
            COUNT(d.id) as total_documents
        FROM documents d
        JOIN subjects s ON d.subject_id = s.id
        WHERE s.level_id = ? AND d.is_approved = 1
    ");
    $stmt->execute([$levelId]);
    return $stmt->fetch();
}

// دالة لجلب أحدث مواضيع الشهادات
function getLatestExamDocuments($levelId, $limit = 6) {
    global $pdo;

    $stmt = $pdo->prepare("
        SELECT d.*, s.name as subject_name, s.color as subject_color
        FROM documents d
        JOIN subjects s ON d.subject_id = s.id
        WHERE s.level_id = ? AND d.type IN ('موضوع شهادة', 'حل شهادة')
        ORDER BY d.exam_year DESC, d.upload_date DESC
        LIMIT ?
    ");
    $stmt->execute([$levelId, $limit]);
    return $stmt->fetchAll();
}

// دالة للتحقق من كون المستوى شهادة رسمية
function isExamLevel($levelId) {
    return in_array($levelId, [4, 5]); // شهادة التعليم المتوسط والبكالوريا
}

// دالة البحث المتقدم في الشهادات
function searchExamDocuments($query = '', $levelId = null, $subjectId = null, $type = null, $examYear = null, $examSession = null) {
    global $pdo;

    $sql = "SELECT d.*, s.name as subject_name, s.color as subject_color, s.icon as subject_icon,
                   l.name as level_name, l.id as level_id
            FROM documents d
            JOIN subjects s ON d.subject_id = s.id
            JOIN levels l ON s.level_id = l.id
            WHERE l.id IN (4, 5) AND d.type IN ('موضوع شهادة', 'حل شهادة')";

    $params = [];

    if ($query) {
        $sql .= " AND (d.title LIKE ? OR d.description LIKE ? OR s.name LIKE ?)";
        $params[] = "%$query%";
        $params[] = "%$query%";
        $params[] = "%$query%";
    }

    if ($levelId) {
        $sql .= " AND l.id = ?";
        $params[] = $levelId;
    }

    if ($subjectId) {
        $sql .= " AND s.id = ?";
        $params[] = $subjectId;
    }

    if ($type) {
        if ($type === 'موضوع') {
            $sql .= " AND d.type = 'موضوع شهادة'";
        } elseif ($type === 'حل') {
            $sql .= " AND d.type = 'حل شهادة'";
        } else {
            $sql .= " AND d.type = ?";
            $params[] = $type;
        }
    }

    if ($examYear) {
        $sql .= " AND d.exam_year = ?";
        $params[] = $examYear;
    }

    if ($examSession) {
        $sql .= " AND d.exam_session = ?";
        $params[] = $examSession;
    }

    $sql .= " ORDER BY d.exam_year DESC, d.upload_date DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// دالة لجلب جميع السنوات المتاحة للشهادات
function getAllExamYears() {
    global $pdo;

    $stmt = $pdo->prepare("
        SELECT DISTINCT exam_year
        FROM documents
        WHERE exam_year IS NOT NULL AND type IN ('موضوع شهادة', 'حل شهادة')
        ORDER BY exam_year DESC
    ");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

// دالة لجلب إحصائيات البحث في الشهادات
function getExamSearchStats($query = '', $levelId = null, $examYear = null) {
    global $pdo;

    $sql = "SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN d.type = 'موضوع شهادة' THEN 1 END) as subjects,
                COUNT(CASE WHEN d.type = 'حل شهادة' THEN 1 END) as solutions,
                COUNT(DISTINCT d.exam_year) as years,
                COUNT(DISTINCT s.id) as subjects_count
            FROM documents d
            JOIN subjects s ON d.subject_id = s.id
            JOIN levels l ON s.level_id = l.id
            WHERE l.id IN (4, 5) AND d.type IN ('موضوع شهادة', 'حل شهادة')";

    $params = [];

    if ($query) {
        $sql .= " AND (d.title LIKE ? OR d.description LIKE ? OR s.name LIKE ?)";
        $params[] = "%$query%";
        $params[] = "%$query%";
        $params[] = "%$query%";
    }

    if ($levelId) {
        $sql .= " AND l.id = ?";
        $params[] = $levelId;
    }

    if ($examYear) {
        $sql .= " AND d.exam_year = ?";
        $params[] = $examYear;
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetch();
}

// دالة لزيادة عداد التحميلات
function incrementDownloadCount($documentId) {
    global $pdo;
    $stmt = $pdo->prepare("UPDATE documents SET download_count = download_count + 1 WHERE id = ?");
    return $stmt->execute([$documentId]);
}

// دالة للحصول على إحصائيات الموقع
function getSiteStats() {
    global $pdo;
    
    $stats = [];
    $stats['total_documents'] = $pdo->query("SELECT COUNT(*) FROM documents")->fetchColumn();
    $stats['total_users'] = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
    $stats['total_downloads'] = $pdo->query("SELECT SUM(download_count) FROM documents")->fetchColumn();
    $stats['lessons'] = $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'درس'")->fetchColumn();
    $stats['exams'] = $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'امتحان'")->fetchColumn();
    $stats['solutions'] = $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'حل'")->fetchColumn();
    
    return $stats;
}

// دالة لتسجيل العمليات
function logActivity($userId, $action, $details = '') {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())");
    return $stmt->execute([$userId, $action, $details]);
}

// إعدادات الموقع
$site_settings = [
    'site_name' => 'منصة التعليم',
    'site_description' => 'منصة تعليمية شاملة للدروس والامتحانات',
    'admin_email' => '<EMAIL>',
    'max_file_size' => 10 * 1024 * 1024, // 10MB
    'allowed_file_types' => ['pdf', 'doc', 'docx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png'],
    'pagination_limit' => 12,
    'enable_registration' => true,
    'enable_comments' => true,
    'maintenance_mode' => false
];

// التحقق من وضع الصيانة
if ($site_settings['maintenance_mode'] && !isAdmin()) {
    if (!in_array(basename($_SERVER['PHP_SELF']), ['login.php', 'maintenance.php'])) {
        redirect('maintenance.php');
    }
}
?>
