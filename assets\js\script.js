// تفعيل القائمة المتجاوبة
document.addEventListener('DOMContentLoaded', function() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!mobileToggle.contains(e.target) && !navMenu.contains(e.target)) {
            navMenu.classList.remove('active');
        }
    });

    // تفعيل القوائم المنسدلة
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const menu = dropdown.querySelector('.dropdown-menu');
        
        dropdown.addEventListener('mouseenter', function() {
            menu.style.opacity = '1';
            menu.style.visibility = 'visible';
            menu.style.transform = 'translateY(0)';
        });
        
        dropdown.addEventListener('mouseleave', function() {
            menu.style.opacity = '0';
            menu.style.visibility = 'hidden';
            menu.style.transform = 'translateY(-10px)';
        });
    });

    // تأثيرات التمرير السلس
    const links = document.querySelectorAll('a[href^="#"]');
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // تأثير الظهور عند التمرير
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // تطبيق التأثير على البطاقات
    const cards = document.querySelectorAll('.level-card, .content-card, .stat-card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // تأثير العد للإحصائيات
    const statNumbers = document.querySelectorAll('.stat-content h4');
    const countUp = (element, target) => {
        let current = 0;
        const increment = target / 100;
        const timer = setInterval(() => {
            current += increment;
            element.textContent = Math.floor(current);
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 20);
    };

    const statsObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.textContent);
                countUp(entry.target, target);
                statsObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });

    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });

    // تحسين تجربة البحث
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // يمكن إضافة البحث المباشر هنا
                console.log('البحث عن:', this.value);
            }, 300);
        });
    }

    // تأكيد التحميل
    const downloadLinks = document.querySelectorAll('a[href*="download.php"]');
    downloadLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const fileName = this.closest('.content-card').querySelector('h4').textContent;
            if (!confirm(`هل تريد تحميل: ${fileName}؟`)) {
                e.preventDefault();
            }
        });
    });

    // إضافة تأثير التحميل للأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                const originalText = this.textContent;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحميل...';
                
                setTimeout(() => {
                    this.classList.remove('loading');
                    this.textContent = originalText;
                }, 2000);
            }
        });
    });

    // تحسين الأداء - تأخير تحميل الصور
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => {
        imageObserver.observe(img);
    });

    // إضافة الوضع الليلي
    const darkModeToggle = document.querySelector('.dark-mode-toggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        });

        // تطبيق الوضع المحفوظ
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
        }
    }

    // تحسين تجربة النماذج
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
            }
        });
    });

    // إضافة تأثيرات الكتابة
    const typewriterElements = document.querySelectorAll('.typewriter');
    typewriterElements.forEach(element => {
        const text = element.textContent;
        element.textContent = '';
        let i = 0;
        const typeInterval = setInterval(() => {
            element.textContent += text.charAt(i);
            i++;
            if (i > text.length) {
                clearInterval(typeInterval);
            }
        }, 100);
    });

    // إضافة تأثير الجسيمات المتقدم للخلفية
    function createAdvancedParticles() {
        const particlesContainer = document.getElementById('particles');
        if (!particlesContainer) return;

        // إنشاء جسيمات متحركة
        for (let i = 0; i < 80; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            const size = Math.random() * 6 + 2;
            const duration = Math.random() * 8 + 4;
            const delay = Math.random() * 5;
            const opacity = Math.random() * 0.8 + 0.2;

            particle.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                background: rgba(255,255,255,${opacity});
                border-radius: 50%;
                pointer-events: none;
                animation: particleFloat ${duration}s infinite ease-in-out;
                left: ${Math.random() * 100}%;
                top: 100%;
                animation-delay: ${delay}s;
                box-shadow: 0 0 ${size * 2}px rgba(255,255,255,0.5);
            `;
            particlesContainer.appendChild(particle);
        }

        // إضافة جسيمات كبيرة للتأثير
        for (let i = 0; i < 20; i++) {
            const bigParticle = document.createElement('div');
            bigParticle.className = 'particle';

            const size = Math.random() * 12 + 8;
            const duration = Math.random() * 15 + 10;
            const delay = Math.random() * 8;

            bigParticle.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                background: radial-gradient(circle, rgba(255,255,255,0.6), transparent);
                border-radius: 50%;
                pointer-events: none;
                animation: particleFloat ${duration}s infinite ease-in-out;
                left: ${Math.random() * 100}%;
                top: 100%;
                animation-delay: ${delay}s;
                filter: blur(1px);
            `;
            particlesContainer.appendChild(bigParticle);
        }
    }

    // إضافة CSS للتأثيرات المتقدمة
    const advancedStyle = document.createElement('style');
    advancedStyle.textContent = `
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .hero {
            position: relative;
            overflow: hidden;
        }

        .particles-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* تحسينات إضافية للبطاقات */
        .level-card, .content-card, .stat-card {
            will-change: transform, box-shadow;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        /* تأثيرات الإضاءة */
        .glow-effect {
            position: relative;
        }

        .glow-effect::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
            border-radius: inherit;
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
            animation: glowing 2s linear infinite;
            background-size: 400% 400%;
        }

        .glow-effect:hover::before {
            opacity: 1;
        }

        @keyframes glowing {
            0% { background-position: 0 0; }
            50% { background-position: 400% 0; }
            100% { background-position: 0 0; }
        }
    `;
    document.head.appendChild(advancedStyle);

    createAdvancedParticles();

    // إضافة تأثيرات تفاعلية للبطاقات
    function addCardInteractions() {
        const cards = document.querySelectorAll('.level-card, .content-card, .stat-card');

        cards.forEach(card => {
            // تأثير الماوس
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-15px) scale(1.05)';
                this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.3), 0 0 30px rgba(52, 152, 219, 0.5)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
            });

            // تأثير النقر
            card.addEventListener('mousedown', function() {
                this.style.transform = 'translateY(-10px) scale(0.98)';
            });

            card.addEventListener('mouseup', function() {
                this.style.transform = 'translateY(-15px) scale(1.05)';
            });
        });
    }

    addCardInteractions();

    // تأثيرات متقدمة للأزرار
    function addAdvancedButtonEffects() {
        const buttons = document.querySelectorAll('.btn');

        buttons.forEach(button => {
            // إضافة تأثير الموجة عند النقر
            button.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const ripple = document.createElement('span');
                ripple.style.cssText = `
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255,255,255,0.6);
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    left: ${x}px;
                    top: ${y}px;
                    width: 20px;
                    height: 20px;
                    margin-left: -10px;
                    margin-top: -10px;
                `;

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    addAdvancedButtonEffects();

    // تحسين تنظيم البطاقات ديناميكياً
    function optimizeCardLayout() {
        const grids = document.querySelectorAll('.levels-grid, .content-grid, .stats-grid, .subjects-grid, .documents-grid');

        grids.forEach(grid => {
            const cards = grid.querySelectorAll('.level-card, .content-card, .stat-card, .subject-card, .document-card');

            // توحيد ارتفاع البطاقات في نفس الصف
            function equalizeCardHeights() {
                // إعادة تعيين الارتفاعات
                cards.forEach(card => {
                    card.style.height = 'auto';
                });

                // حساب الارتفاع الأقصى
                let maxHeight = 0;
                cards.forEach(card => {
                    const height = card.offsetHeight;
                    if (height > maxHeight) {
                        maxHeight = height;
                    }
                });

                // تطبيق الارتفاع الموحد
                cards.forEach(card => {
                    card.style.height = maxHeight + 'px';
                });
            }

            // تطبيق التحسين عند تحميل الصفحة وتغيير حجم النافذة
            if (cards.length > 0) {
                setTimeout(equalizeCardHeights, 100);
                window.addEventListener('resize', debounce(equalizeCardHeights, 250));
            }
        });
    }

    // دالة للتأخير في تنفيذ الوظائف
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    optimizeCardLayout();

    // تحسين ترتيب البطاقات بناءً على المحتوى
    function smartCardArrangement() {
        const contentGrids = document.querySelectorAll('.content-grid, .documents-grid');

        contentGrids.forEach(grid => {
            const cards = Array.from(grid.children);

            // ترتيب البطاقات حسب نوع المحتوى
            cards.sort((a, b) => {
                const typeA = a.querySelector('.content-type, .document-type')?.textContent?.trim() || '';
                const typeB = b.querySelector('.content-type, .document-type')?.textContent?.trim() || '';

                const typeOrder = { 'درس': 1, 'امتحان': 2, 'حل': 3, 'تمرين': 4, 'ملخص': 5 };

                return (typeOrder[typeA] || 999) - (typeOrder[typeB] || 999);
            });

            // إعادة ترتيب العناصر في DOM
            cards.forEach(card => grid.appendChild(card));
        });
    }

    smartCardArrangement();

    // إضافة تأثير التمرير للهيدر
    let lastScrollTop = 0;
    const header = document.querySelector('.header');

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // إضافة كلاس عند التمرير
        if (scrollTop > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }

        // إخفاء/إظهار الهيدر
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            header.style.transform = 'translateY(-100%)';
        } else {
            header.style.transform = 'translateY(0)';
        }

        lastScrollTop = scrollTop;
    });

    // إضافة تأثيرات الظهور المتقدمة
    const fadeElements = document.querySelectorAll('.fade-in, .fade-in-left, .fade-in-right');
    const fadeObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                fadeObserver.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });

    fadeElements.forEach(element => {
        fadeObserver.observe(element);
    });

    // إضافة تأثير النقر للبطاقات
    const levelCards = document.querySelectorAll('.level-card');
    levelCards.forEach(card => {
        card.addEventListener('click', function() {
            const levelId = this.dataset.levelId;
            if (levelId) {
                window.location.href = `level.php?id=${levelId}`;
            }
        });

        // إضافة تأثير الريبل
        card.classList.add('ripple');
    });

    // تحسين تأثيرات الأزرار
    const allButtons = document.querySelectorAll('.btn, button');
    allButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // إضافة تأثير التمرير السلس للصفحة
    if (CSS.supports('scroll-behavior', 'smooth')) {
        document.documentElement.style.scrollBehavior = 'smooth';
    }
});

// دالة لإظهار الرسائل
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
        <button class="message-close">&times;</button>
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.classList.add('show');
    }, 100);
    
    const closeBtn = messageDiv.querySelector('.message-close');
    closeBtn.addEventListener('click', () => {
        messageDiv.remove();
    });
    
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// دالة للتحقق من الاتصال بالإنترنت
function checkConnection() {
    if (!navigator.onLine) {
        showMessage('لا يوجد اتصال بالإنترنت', 'error');
    }
}

window.addEventListener('online', () => showMessage('تم استعادة الاتصال بالإنترنت', 'success'));
window.addEventListener('offline', () => showMessage('انقطع الاتصال بالإنترنت', 'error'));
