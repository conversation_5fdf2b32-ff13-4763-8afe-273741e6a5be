<?php
session_start();
require_once '../db/config.php';

// التحقق من صلاحيات المعلم أو المدير
if (!isTeacher()) {
    redirect('../index.php');
}

$error = '';
$success = '';

// معالجة رفع الملف
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = sanitize($_POST['title'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $type = sanitize($_POST['type'] ?? '');
    $subject_id = intval($_POST['subject_id'] ?? 0);
    $academic_year = sanitize($_POST['academic_year'] ?? '');
    $semester = sanitize($_POST['semester'] ?? '');
    $difficulty_level = sanitize($_POST['difficulty_level'] ?? 'متوسط');
    $tags = sanitize($_POST['tags'] ?? '');
    
    // التحقق من صحة البيانات
    if (empty($title) || empty($type) || !$subject_id) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        $error = 'يرجى اختيار ملف للرفع';
    } else {
        // رفع الملف
        $uploadResult = uploadFile($_FILES['file']);
        
        if ($uploadResult['success']) {
            try {
                // إدراج الوثيقة في قاعدة البيانات
                $stmt = $pdo->prepare("
                    INSERT INTO documents (
                        title, description, file_path, file_name, file_size, file_type, 
                        type, subject_id, uploaded_by, academic_year, semester, 
                        difficulty_level, tags, upload_date
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
                ");
                
                $fileInfo = pathinfo($_FILES['file']['name']);
                $fileExtension = strtolower($fileInfo['extension']);
                
                if ($stmt->execute([
                    $title, $description, $uploadResult['path'], $uploadResult['filename'],
                    $_FILES['file']['size'], $fileExtension, $type, $subject_id,
                    $_SESSION['user_id'], $academic_year, $semester, $difficulty_level, $tags
                ])) {
                    $documentId = $pdo->lastInsertId();
                    
                    // تسجيل النشاط
                    logActivity($_SESSION['user_id'], 'upload', "رفع الملف: $title");
                    
                    $success = 'تم رفع الملف بنجاح!';
                    
                    // إعادة توجيه بعد 3 ثوان
                    header("refresh:3;url=documents.php");
                } else {
                    // حذف الملف في حالة فشل الإدراج
                    deleteFile($uploadResult['path']);
                    $error = 'حدث خطأ أثناء حفظ معلومات الملف';
                }
            } catch (PDOException $e) {
                deleteFile($uploadResult['path']);
                $error = 'حدث خطأ في قاعدة البيانات';
                error_log($e->getMessage());
            }
        } else {
            $error = $uploadResult['message'];
        }
    }
}

// جلب المستويات والمواد
$levels = getLevels();
$subjects = [];
if (isset($_GET['subject_id'])) {
    $subjectStmt = $pdo->prepare("SELECT * FROM subjects WHERE id = ?");
    $subjectStmt->execute([$_GET['subject_id']]);
    $selectedSubject = $subjectStmt->fetch();
    if ($selectedSubject) {
        $subjects = getSubjectsByLevel($selectedSubject['level_id']);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة ملف جديد - لوحة التحكم</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: 100vh;
        }
        
        .sidebar {
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover {
            background: #34495e;
            color: white;
        }
        
        .main-content {
            background: #f8f9fa;
            padding: 2rem;
        }
        
        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 2rem;
        }
        
        .form-grid {
            display: grid;
            gap: 2rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: bold;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .file-upload {
            border: 2px dashed #e1e8ed;
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }
        
        .file-upload.dragover {
            border-color: #3498db;
            background: #e3f2fd;
        }
        
        .file-upload-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 1rem;
        }
        
        .file-upload-text {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .file-upload-input {
            display: none;
        }
        
        .file-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            display: none;
        }
        
        .file-info.show {
            display: block;
        }
        
        .btn-submit {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-submit:hover {
            background: linear-gradient(135deg, #229954, #27ae60);
            transform: translateY(-2px);
        }
        
        .btn-submit:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }
        
        .alert-error {
            background: #fee;
            color: #c0392b;
            border: 1px solid #f5b7b1;
        }
        
        .alert-success {
            background: #eafaf1;
            color: #27ae60;
            border: 1px solid #a9dfbf;
        }
        
        .form-help {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }
        
        .required {
            color: #e74c3c;
        }
        
        @media (max-width: 768px) {
            .dashboard-layout {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: 2;
                padding: 1rem 0;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                <p>مرحباً، <?= $_SESSION['full_name'] ?></p>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="documents.php"><i class="fas fa-file-alt"></i> إدارة الملفات</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subjects.php"><i class="fas fa-book"></i> إدارة المواد</a></li>
                <li><a href="comments.php"><i class="fas fa-comments"></i> التعليقات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../index.php"><i class="fas fa-arrow-right"></i> العودة للموقع</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-plus"></i> إضافة ملف جديد</h1>
                <p>رفع ملف تعليمي جديد للمنصة</p>
            </div>

            <!-- Form Container -->
            <div class="form-container">
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= $success ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" enctype="multipart/form-data" id="uploadForm">
                    <div class="form-grid">
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                            
                            <div class="form-group">
                                <label for="title">عنوان الملف <span class="required">*</span></label>
                                <input type="text" id="title" name="title" class="form-input" 
                                       value="<?= htmlspecialchars($_POST['title'] ?? '') ?>" 
                                       placeholder="أدخل عنوان الملف" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="description">وصف الملف</label>
                                <textarea id="description" name="description" class="form-textarea" 
                                          placeholder="أدخل وصف مفصل للملف..."><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="type">نوع المحتوى <span class="required">*</span></label>
                                    <select id="type" name="type" class="form-select" required>
                                        <option value="">اختر نوع المحتوى</option>
                                        <option value="درس" <?= ($_POST['type'] ?? '') === 'درس' ? 'selected' : '' ?>>درس</option>
                                        <option value="امتحان" <?= ($_POST['type'] ?? '') === 'امتحان' ? 'selected' : '' ?>>امتحان</option>
                                        <option value="حل" <?= ($_POST['type'] ?? '') === 'حل' ? 'selected' : '' ?>>حل نموذجي</option>
                                        <option value="تمرين" <?= ($_POST['type'] ?? '') === 'تمرين' ? 'selected' : '' ?>>تمرين</option>
                                        <option value="ملخص" <?= ($_POST['type'] ?? '') === 'ملخص' ? 'selected' : '' ?>>ملخص</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="difficulty_level">مستوى الصعوبة</label>
                                    <select id="difficulty_level" name="difficulty_level" class="form-select">
                                        <option value="سهل" <?= ($_POST['difficulty_level'] ?? 'متوسط') === 'سهل' ? 'selected' : '' ?>>سهل</option>
                                        <option value="متوسط" <?= ($_POST['difficulty_level'] ?? 'متوسط') === 'متوسط' ? 'selected' : '' ?>>متوسط</option>
                                        <option value="صعب" <?= ($_POST['difficulty_level'] ?? 'متوسط') === 'صعب' ? 'selected' : '' ?>>صعب</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Subject Selection -->
                        <div class="form-section">
                            <h3><i class="fas fa-book"></i> تصنيف المحتوى</h3>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="level">المستوى التعليمي <span class="required">*</span></label>
                                    <select id="level" name="level" class="form-select" onchange="loadSubjects(this.value)" required>
                                        <option value="">اختر المستوى</option>
                                        <?php foreach($levels as $level): ?>
                                            <option value="<?= $level['id'] ?>"><?= $level['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="subject_id">المادة الدراسية <span class="required">*</span></label>
                                    <select id="subject_id" name="subject_id" class="form-select" required>
                                        <option value="">اختر المادة</option>
                                        <?php foreach($subjects as $subject): ?>
                                            <option value="<?= $subject['id'] ?>" <?= ($_GET['subject_id'] ?? '') == $subject['id'] ? 'selected' : '' ?>>
                                                <?= $subject['name'] ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="academic_year">السنة الدراسية</label>
                                    <input type="text" id="academic_year" name="academic_year" class="form-input" 
                                           value="<?= htmlspecialchars($_POST['academic_year'] ?? date('Y') . '-' . (date('Y') + 1)) ?>" 
                                           placeholder="2024-2025">
                                </div>
                                
                                <div class="form-group">
                                    <label for="semester">الفصل الدراسي</label>
                                    <select id="semester" name="semester" class="form-select">
                                        <option value="">اختر الفصل</option>
                                        <option value="الأول" <?= ($_POST['semester'] ?? '') === 'الأول' ? 'selected' : '' ?>>الفصل الأول</option>
                                        <option value="الثاني" <?= ($_POST['semester'] ?? '') === 'الثاني' ? 'selected' : '' ?>>الفصل الثاني</option>
                                        <option value="الثالث" <?= ($_POST['semester'] ?? '') === 'الثالث' ? 'selected' : '' ?>>الفصل الثالث</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="tags">الكلمات المفتاحية</label>
                                <input type="text" id="tags" name="tags" class="form-input" 
                                       value="<?= htmlspecialchars($_POST['tags'] ?? '') ?>" 
                                       placeholder="مثال: جبر، معادلات، رياضيات (افصل بفواصل)">
                                <div class="form-help">أدخل كلمات مفتاحية لتسهيل البحث، افصل بينها بفواصل</div>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="form-section">
                            <h3><i class="fas fa-upload"></i> رفع الملف</h3>
                            
                            <div class="file-upload" onclick="document.getElementById('file').click()">
                                <div class="file-upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <div class="file-upload-text">
                                    <strong>انقر لاختيار ملف</strong> أو اسحب الملف هنا
                                </div>
                                <div class="form-help">
                                    الأنواع المدعومة: PDF, DOC, DOCX, PPT, PPTX, JPG, PNG<br>
                                    الحد الأقصى للحجم: 10 ميجابايت
                                </div>
                                <input type="file" id="file" name="file" class="file-upload-input" 
                                       accept=".pdf,.doc,.docx,.ppt,.pptx,.jpg,.jpeg,.png" required>
                            </div>
                            
                            <div class="file-info" id="fileInfo">
                                <h4>معلومات الملف:</h4>
                                <p id="fileName"></p>
                                <p id="fileSize"></p>
                                <p id="fileType"></p>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="form-section">
                            <button type="submit" class="btn-submit" id="submitBtn">
                                <i class="fas fa-upload"></i> رفع الملف
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </main>
    </div>

    <script src="../assets/js/script.js"></script>
    <script>
        // تحميل المواد حسب المستوى
        function loadSubjects(levelId) {
            const subjectSelect = document.getElementById('subject_id');
            subjectSelect.innerHTML = '<option value="">اختر المادة</option>';
            
            if (levelId) {
                fetch(`../ajax/get_subjects.php?level_id=${levelId}`)
                    .then(response => response.json())
                    .then(subjects => {
                        subjects.forEach(subject => {
                            const option = document.createElement('option');
                            option.value = subject.id;
                            option.textContent = subject.name;
                            subjectSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error loading subjects:', error));
            }
        }

        // معالجة رفع الملف
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('file');
            const fileInfo = document.getElementById('fileInfo');
            const fileUpload = document.querySelector('.file-upload');
            const form = document.getElementById('uploadForm');
            const submitBtn = document.getElementById('submitBtn');

            // معالجة اختيار الملف
            fileInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    const file = this.files[0];
                    showFileInfo(file);
                }
            });

            // معالجة السحب والإفلات
            fileUpload.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            fileUpload.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            fileUpload.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    showFileInfo(files[0]);
                }
            });

            // عرض معلومات الملف
            function showFileInfo(file) {
                document.getElementById('fileName').textContent = `الاسم: ${file.name}`;
                document.getElementById('fileSize').textContent = `الحجم: ${formatFileSize(file.size)}`;
                document.getElementById('fileType').textContent = `النوع: ${file.type || 'غير محدد'}`;
                fileInfo.classList.add('show');
            }

            // تنسيق حجم الملف
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // معالجة إرسال النموذج
            form.addEventListener('submit', function(e) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الرفع...';
            });

            // تعيين المستوى والمادة إذا تم تمريرهما في URL
            <?php if (isset($_GET['subject_id']) && $selectedSubject): ?>
                document.getElementById('level').value = '<?= $selectedSubject['level_id'] ?>';
                loadSubjects('<?= $selectedSubject['level_id'] ?>');
                setTimeout(() => {
                    document.getElementById('subject_id').value = '<?= $_GET['subject_id'] ?>';
                }, 500);
            <?php endif; ?>
        });
    </script>
</body>
</html>
