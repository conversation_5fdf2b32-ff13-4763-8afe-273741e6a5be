<?php
session_start();
require_once 'db/config.php';

// التحقق من وجود معرف المادة
$subjectId = $_GET['id'] ?? null;
if (!$subjectId || !is_numeric($subjectId)) {
    redirect('index.php');
}

// جلب معلومات المادة والمستوى
$stmt = $pdo->prepare("
    SELECT s.*, l.name as level_name, l.id as level_id 
    FROM subjects s 
    JOIN levels l ON s.level_id = l.id 
    WHERE s.id = ?
");
$stmt->execute([$subjectId]);
$subject = $stmt->fetch();

if (!$subject) {
    redirect('index.php');
}

// معالجة الفلترة والترتيب
$type = $_GET['type'] ?? '';
$sort = $_GET['sort'] ?? 'newest';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// بناء استعلام الوثائق
$whereClause = "WHERE d.subject_id = ?";
$params = [$subjectId];

if ($type && in_array($type, ['درس', 'امتحان', 'حل', 'تمرين', 'ملخص'])) {
    $whereClause .= " AND d.type = ?";
    $params[] = $type;
}

$orderClause = match($sort) {
    'oldest' => 'ORDER BY d.upload_date ASC',
    'title' => 'ORDER BY d.title ASC',
    'downloads' => 'ORDER BY d.download_count DESC',
    default => 'ORDER BY d.upload_date DESC'
};

// جلب الوثائق
$documentsStmt = $pdo->prepare("
    SELECT d.*, u.full_name as uploader_name
    FROM documents d
    LEFT JOIN users u ON d.uploaded_by = u.id
    $whereClause AND d.is_approved = 1
    $orderClause
    LIMIT $limit OFFSET $offset
");
$documentsStmt->execute($params);
$documents = $documentsStmt->fetchAll();

// عد إجمالي الوثائق للترقيم
$countStmt = $pdo->prepare("
    SELECT COUNT(*) 
    FROM documents d 
    $whereClause AND d.is_approved = 1
");
$countStmt->execute($params);
$totalDocuments = $countStmt->fetchColumn();
$totalPages = ceil($totalDocuments / $limit);

// جلب إحصائيات المادة
$statsStmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_documents,
        SUM(CASE WHEN type = 'درس' THEN 1 ELSE 0 END) as lessons_count,
        SUM(CASE WHEN type = 'امتحان' THEN 1 ELSE 0 END) as exams_count,
        SUM(CASE WHEN type = 'حل' THEN 1 ELSE 0 END) as solutions_count,
        SUM(CASE WHEN type = 'تمرين' THEN 1 ELSE 0 END) as exercises_count,
        SUM(CASE WHEN type = 'ملخص' THEN 1 ELSE 0 END) as summaries_count,
        SUM(download_count) as total_downloads
    FROM documents 
    WHERE subject_id = ? AND is_approved = 1
");
$statsStmt->execute([$subjectId]);
$stats = $statsStmt->fetch();

// جلب جميع المستويات للقائمة الجانبية
$allLevels = getLevels();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $subject['name'] ?> - <?= $subject['level_name'] ?> - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .subject-header {
            background: linear-gradient(135deg, <?= $subject['color'] ?? '#3498db' ?>, <?= adjustBrightness($subject['color'] ?? '#3498db', -20) ?>);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .subject-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .subject-header .level-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 1rem;
        }
        
        .stats-bar {
            background: white;
            padding: 2rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            text-align: center;
        }
        
        .stat-item {
            padding: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: <?= $subject['color'] ?? '#3498db' ?>;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .filters-section {
            background: #f8f9fa;
            padding: 2rem 0;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .filters-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .filter-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .filter-select {
            padding: 0.7rem 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: white;
            font-size: 0.9rem;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: <?= $subject['color'] ?? '#3498db' ?>;
        }
        
        .type-filters {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .type-filter {
            padding: 0.5rem 1rem;
            border: 2px solid #e1e8ed;
            background: white;
            color: #666;
            text-decoration: none;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .type-filter:hover,
        .type-filter.active {
            background: <?= $subject['color'] ?? '#3498db' ?>;
            color: white;
            border-color: <?= $subject['color'] ?? '#3498db' ?>;
        }
        
        .documents-section {
            padding: 3rem 0;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 2rem;
            color: #2c3e50;
        }
        
        .results-info {
            color: #666;
            font-size: 0.9rem;
        }
        
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
            align-items: stretch;
            justify-content: center;
            max-width: 1400px;
            margin-left: auto;
            margin-right: auto;
        }

        @media (min-width: 1200px) {
            .documents-grid {
                grid-template-columns: repeat(3, 1fr);
                max-width: 1200px;
            }
        }

        @media (min-width: 768px) and (max-width: 1199px) {
            .documents-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
        }
        
        .document-card {
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 350px;
            height: 100%;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .document-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .document-card:hover::before {
            transform: scaleX(1);
        }

        .document-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2), 0 0 25px rgba(52, 152, 219, 0.3);
        }
        
        .document-type {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: <?= $subject['color'] ?? '#3498db' ?>;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .document-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            margin-top: 1rem;
            font-size: 1.4rem;
            font-weight: 600;
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 60px;
            line-height: 1.4;
        }

        .document-type {
            flex-shrink: 0;
            margin-bottom: 1rem;
        }

        .document-meta {
            flex-shrink: 0;
            margin-bottom: 1rem;
        }

        .document-description {
            flex-grow: 1;
            margin-bottom: 1.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 60px;
            color: #666;
            line-height: 1.5;
        }

        .document-actions {
            margin-top: auto;
            margin-bottom: 1rem;
            flex-shrink: 0;
        }

        .document-footer {
            flex-shrink: 0;
            border-top: 1px solid #eee;
            padding-top: 1rem;
            margin-top: 1rem;
        }
        
        .document-meta {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .document-actions {
            display: flex;
            gap: 1rem;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.7rem 1rem;
            border: 2px solid #e1e8ed;
            background: white;
            color: #666;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .pagination a:hover,
        .pagination .current {
            background: <?= $subject['color'] ?? '#3498db' ?>;
            color: white;
            border-color: <?= $subject['color'] ?? '#3498db' ?>;
        }
        
        .no-documents {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }
        
        .no-documents i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ddd;
        }
        
        @media (max-width: 768px) {
            .subject-header h1 {
                font-size: 2rem;
            }
            
            .filters-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                justify-content: center;
            }
            
            .type-filters {
                justify-content: center;
            }
            
            .section-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .documents-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                padding: 0 1rem;
            }

            .document-card {
                min-height: 300px;
                padding: 1.5rem;
            }

            .document-card h3 {
                font-size: 1.2rem;
                min-height: 40px;
            }

            .document-description {
                -webkit-line-clamp: 2;
                min-height: 40px;
            }
            
            .document-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="level.php?id=<?= $subject['level_id'] ?>"><i class="fas fa-layer-group"></i> <?= $subject['level_name'] ?></a></li>
                    <li><a href="search.php"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isLoggedIn()): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if(isAdmin()): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Subject Header -->
    <section class="subject-header">
        <div class="container">
            <div class="level-badge"><?= $subject['level_name'] ?></div>
            <h1><i class="<?= $subject['icon'] ?? 'fas fa-book' ?>"></i> <?= $subject['name'] ?></h1>
            <p><?= $subject['description'] ?? 'جميع الدروس والامتحانات والحلول النموذجية' ?></p>
        </div>
    </section>

    <!-- Breadcrumb -->
    <section class="breadcrumb">
        <div class="container">
            <ul class="breadcrumb-list">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><i class="fas fa-chevron-left"></i></li>
                <li><a href="level.php?id=<?= $subject['level_id'] ?>"><?= $subject['level_name'] ?></a></li>
                <li><i class="fas fa-chevron-left"></i></li>
                <li><?= $subject['name'] ?></li>
            </ul>
        </div>
    </section>

    <!-- Stats Bar -->
    <section class="stats-bar">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['total_documents'] ?></div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['lessons_count'] ?></div>
                    <div class="stat-label">درس</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['exams_count'] ?></div>
                    <div class="stat-label">امتحان</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['solutions_count'] ?></div>
                    <div class="stat-label">حل نموذجي</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $stats['total_downloads'] ?></div>
                    <div class="stat-label">تحميل</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="filters-section">
        <div class="container">
            <div class="filters-container">
                <div class="type-filters">
                    <a href="?id=<?= $subjectId ?>&sort=<?= $sort ?>" class="type-filter <?= empty($type) ? 'active' : '' ?>">
                        <i class="fas fa-list"></i> الكل
                    </a>
                    <a href="?id=<?= $subjectId ?>&type=درس&sort=<?= $sort ?>" class="type-filter <?= $type === 'درس' ? 'active' : '' ?>">
                        <i class="fas fa-book"></i> دروس
                    </a>
                    <a href="?id=<?= $subjectId ?>&type=امتحان&sort=<?= $sort ?>" class="type-filter <?= $type === 'امتحان' ? 'active' : '' ?>">
                        <i class="fas fa-file-alt"></i> امتحانات
                    </a>
                    <a href="?id=<?= $subjectId ?>&type=حل&sort=<?= $sort ?>" class="type-filter <?= $type === 'حل' ? 'active' : '' ?>">
                        <i class="fas fa-check-circle"></i> حلول
                    </a>
                    <a href="?id=<?= $subjectId ?>&type=تمرين&sort=<?= $sort ?>" class="type-filter <?= $type === 'تمرين' ? 'active' : '' ?>">
                        <i class="fas fa-pencil-alt"></i> تمارين
                    </a>
                    <a href="?id=<?= $subjectId ?>&type=ملخص&sort=<?= $sort ?>" class="type-filter <?= $type === 'ملخص' ? 'active' : '' ?>">
                        <i class="fas fa-clipboard-list"></i> ملخصات
                    </a>
                </div>
                
                <div class="filter-group">
                    <label for="sort">ترتيب حسب:</label>
                    <select id="sort" class="filter-select" onchange="changeSortOrder(this.value)">
                        <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>الأحدث</option>
                        <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>الأقدم</option>
                        <option value="title" <?= $sort === 'title' ? 'selected' : '' ?>>الاسم</option>
                        <option value="downloads" <?= $sort === 'downloads' ? 'selected' : '' ?>>الأكثر تحميلاً</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Documents Section -->
    <section class="documents-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <?php if($type): ?>
                        <?= $type === 'درس' ? 'الدروس' : ($type === 'امتحان' ? 'الامتحانات' : ($type === 'حل' ? 'الحلول النموذجية' : ($type === 'تمرين' ? 'التمارين' : 'الملخصات'))) ?>
                    <?php else: ?>
                        جميع الملفات
                    <?php endif; ?>
                </h2>
                <div class="results-info">
                    عرض <?= count($documents) ?> من أصل <?= $totalDocuments ?> ملف
                </div>
            </div>

            <?php if($documents): ?>
                <div class="documents-grid">
                    <?php foreach($documents as $doc): ?>
                        <div class="document-card">
                            <div class="document-type">
                                <i class="fas fa-<?= $doc['type'] == 'درس' ? 'book' : ($doc['type'] == 'امتحان' ? 'file-alt' : ($doc['type'] == 'حل' ? 'check-circle' : ($doc['type'] == 'تمرين' ? 'pencil-alt' : 'clipboard-list'))) ?>"></i>
                                <?= $doc['type'] ?>
                            </div>
                            
                            <h3><?= htmlspecialchars($doc['title']) ?></h3>
                            
                            <div class="document-meta">
                                <div class="meta-item">
                                    <i class="fas fa-user"></i>
                                    <span><?= $doc['uploader_name'] ?? 'غير محدد' ?></span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-calendar"></i>
                                    <span><?= date('Y-m-d', strtotime($doc['upload_date'])) ?></span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-download"></i>
                                    <span><?= $doc['download_count'] ?> تحميل</span>
                                </div>
                                <?php if($doc['file_size']): ?>
                                <div class="meta-item">
                                    <i class="fas fa-file"></i>
                                    <span><?= formatFileSize($doc['file_size']) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if($doc['description']): ?>
                                <p class="document-description"><?= htmlspecialchars(substr($doc['description'], 0, 100)) ?>...</p>
                            <?php endif; ?>
                            
                            <div class="document-actions">
                                <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-primary">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if($totalPages > 1): ?>
                    <div class="pagination">
                        <?php if($page > 1): ?>
                            <a href="?id=<?= $subjectId ?>&type=<?= $type ?>&sort=<?= $sort ?>&page=<?= $page - 1 ?>">
                                <i class="fas fa-chevron-right"></i> السابق
                            </a>
                        <?php endif; ?>
                        
                        <?php for($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                            <?php if($i == $page): ?>
                                <span class="current"><?= $i ?></span>
                            <?php else: ?>
                                <a href="?id=<?= $subjectId ?>&type=<?= $type ?>&sort=<?= $sort ?>&page=<?= $i ?>"><?= $i ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>
                        
                        <?php if($page < $totalPages): ?>
                            <a href="?id=<?= $subjectId ?>&type=<?= $type ?>&sort=<?= $sort ?>&page=<?= $page + 1 ?>">
                                التالي <i class="fas fa-chevron-left"></i>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="no-documents">
                    <i class="fas fa-folder-open"></i>
                    <h3>لا توجد ملفات</h3>
                    <p>لم يتم العثور على أي ملفات في هذا القسم حالياً</p>
                    <?php if(isTeacher()): ?>
                        <a href="dashboard/add_document.php?subject_id=<?= $subjectId ?>" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة ملف جديد
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>المستويات</h4>
                    <ul>
                        <?php foreach($allLevels as $lvl): ?>
                            <li><a href="level.php?id=<?= $lvl['id'] ?>"><?= $lvl['name'] ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
    <script src="assets/js/enhancements.js"></script>
    <script>
        function changeSortOrder(sort) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sort);
            url.searchParams.set('page', '1'); // إعادة تعيين الصفحة للأولى
            window.location.href = url.toString();
        }
    </script>
</body>
</html>

<?php
// دالة مساعدة لتعديل سطوع اللون
function adjustBrightness($hex, $percent) {
    $hex = str_replace('#', '', $hex);
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));
    
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}
?>
