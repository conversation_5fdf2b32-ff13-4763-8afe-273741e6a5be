-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS school_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE school_platform;

-- جدول المستويات التعليمية
CREATE TABLE levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول المواد الدراسية
CREATE TABLE subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'fas fa-book',
    color VARCHAR(7) DEFAULT '#3498db',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('student', 'teacher', 'admin') DEFAULT 'student',
    avatar VARCHAR(255) DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    bio TEXT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255) DEFAULT NULL,
    reset_token VARCHAR(255) DEFAULT NULL,
    reset_expires TIMESTAMP NULL,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الوثائق (الدروس والامتحانات والحلول)
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(10) NOT NULL,
    type ENUM('درس', 'امتحان', 'حل', 'تمرين', 'ملخص', 'موضوع شهادة', 'حل شهادة') NOT NULL,
    subject_id INT NOT NULL,
    uploaded_by INT NOT NULL,
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    tags TEXT DEFAULT NULL,
    academic_year VARCHAR(20) DEFAULT NULL,
    exam_year VARCHAR(10) DEFAULT NULL,
    exam_session ENUM('الدورة العادية', 'دورة الاستدراك', 'دورة استثنائية') DEFAULT NULL,
    branch VARCHAR(100) DEFAULT NULL,
    semester ENUM('الأول', 'الثاني', 'الثالث') DEFAULT NULL,
    difficulty_level ENUM('سهل', 'متوسط', 'صعب') DEFAULT 'متوسط',
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_type (type),
    INDEX idx_subject (subject_id),
    INDEX idx_upload_date (upload_date),
    FULLTEXT(title, description, tags)
);

-- جدول التقييمات
CREATE TABLE ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_rating (document_id, user_id)
);

-- جدول التعليقات
CREATE TABLE comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    parent_id INT DEFAULT NULL,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE
);

-- جدول المفضلة
CREATE TABLE favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    document_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (user_id, document_id)
);

-- جدول سجل الأنشطة
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- جدول الإعدادات
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT DEFAULT NULL,
    description TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_created_at (created_at)
);

-- جدول السنوات الدراسية للشهادات
CREATE TABLE exam_years (
    id INT AUTO_INCREMENT PRIMARY KEY,
    year VARCHAR(10) NOT NULL,
    level_id INT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_year_level (year, level_id),
    INDEX idx_level_year (level_id, year),
    INDEX idx_active (is_active)
);

-- إدراج البيانات الأولية

-- إدراج المستويات التعليمية
INSERT INTO levels (name, description) VALUES
('الابتدائي', 'المرحلة الابتدائية من الصف الأول إلى الخامس'),
('المتوسط', 'المرحلة المتوسطة من الصف السادس إلى التاسع'),
('الثانوي', 'المرحلة الثانوية من الصف العاشر إلى الثاني عشر'),
('شهادة التعليم المتوسط', 'مواضيع وحلول امتحانات شهادة التعليم المتوسط حسب السنوات'),
('شهادة البكالوريا', 'مواضيع وحلول امتحانات شهادة البكالوريا حسب السنوات والشعب');

-- إدراج المواد الدراسية للمرحلة الابتدائية
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
(1, 'الرياضيات', 'مادة الرياضيات للمرحلة الابتدائية', 'fas fa-calculator', '#e74c3c'),
(1, 'اللغة العربية', 'مادة اللغة العربية للمرحلة الابتدائية', 'fas fa-language', '#2ecc71'),
(1, 'العلوم', 'مادة العلوم للمرحلة الابتدائية', 'fas fa-flask', '#9b59b6'),
(1, 'التربية الإسلامية', 'مادة التربية الإسلامية للمرحلة الابتدائية', 'fas fa-mosque', '#27ae60'),
(1, 'التاريخ', 'مادة التاريخ للمرحلة الابتدائية', 'fas fa-landmark', '#f39c12'),
(1, 'الجغرافيا', 'مادة الجغرافيا للمرحلة الابتدائية', 'fas fa-globe', '#3498db');

-- إدراج المواد الدراسية للمرحلة المتوسطة
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
(2, 'الرياضيات', 'مادة الرياضيات للمرحلة المتوسطة', 'fas fa-calculator', '#e74c3c'),
(2, 'اللغة العربية', 'مادة اللغة العربية للمرحلة المتوسطة', 'fas fa-language', '#2ecc71'),
(2, 'العلوم الفيزيائية', 'مادة العلوم الفيزيائية للمرحلة المتوسطة', 'fas fa-atom', '#9b59b6'),
(2, 'علوم الطبيعة والحياة', 'مادة علوم الطبيعة والحياة للمرحلة المتوسطة', 'fas fa-leaf', '#27ae60'),
(2, 'التاريخ والجغرافيا', 'مادة التاريخ والجغرافيا للمرحلة المتوسطة', 'fas fa-map', '#f39c12'),
(2, 'التربية الإسلامية', 'مادة التربية الإسلامية للمرحلة المتوسطة', 'fas fa-mosque', '#27ae60'),
(2, 'اللغة الفرنسية', 'مادة اللغة الفرنسية للمرحلة المتوسطة', 'fas fa-language', '#3498db'),
(2, 'اللغة الإنجليزية', 'مادة اللغة الإنجليزية للمرحلة المتوسطة', 'fas fa-language', '#e67e22');

-- إدراج المواد الدراسية للمرحلة الثانوية
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
(3, 'الرياضيات', 'مادة الرياضيات للمرحلة الثانوية', 'fas fa-calculator', '#e74c3c'),
(3, 'الفيزياء', 'مادة الفيزياء للمرحلة الثانوية', 'fas fa-atom', '#9b59b6'),
(3, 'الكيمياء', 'مادة الكيمياء للمرحلة الثانوية', 'fas fa-flask', '#1abc9c'),
(3, 'علوم الطبيعة والحياة', 'مادة علوم الطبيعة والحياة للمرحلة الثانوية', 'fas fa-dna', '#27ae60'),
(3, 'اللغة العربية وآدابها', 'مادة اللغة العربية وآدابها للمرحلة الثانوية', 'fas fa-book-open', '#2ecc71'),
(3, 'التاريخ والجغرافيا', 'مادة التاريخ والجغرافيا للمرحلة الثانوية', 'fas fa-globe-americas', '#f39c12'),
(3, 'الفلسفة', 'مادة الفلسفة للمرحلة الثانوية', 'fas fa-brain', '#34495e'),
(3, 'اللغة الفرنسية', 'مادة اللغة الفرنسية للمرحلة الثانوية', 'fas fa-language', '#3498db'),
(3, 'اللغة الإنجليزية', 'مادة اللغة الإنجليزية للمرحلة الثانوية', 'fas fa-language', '#e67e22'),
(3, 'التربية الإسلامية', 'مادة التربية الإسلامية للمرحلة الثانوية', 'fas fa-mosque', '#27ae60');

-- إدراج المواد الدراسية لشهادة التعليم المتوسط
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
(4, 'اللغة العربية', 'مواضيع وحلول اللغة العربية لشهادة التعليم المتوسط', 'fas fa-book-open', '#2ecc71'),
(4, 'الرياضيات', 'مواضيع وحلول الرياضيات لشهادة التعليم المتوسط', 'fas fa-calculator', '#e74c3c'),
(4, 'العلوم الفيزيائية', 'مواضيع وحلول العلوم الفيزيائية لشهادة التعليم المتوسط', 'fas fa-atom', '#9b59b6'),
(4, 'علوم الطبيعة والحياة', 'مواضيع وحلول علوم الطبيعة والحياة لشهادة التعليم المتوسط', 'fas fa-leaf', '#27ae60'),
(4, 'التاريخ والجغرافيا', 'مواضيع وحلول التاريخ والجغرافيا لشهادة التعليم المتوسط', 'fas fa-map', '#f39c12'),
(4, 'التربية الإسلامية', 'مواضيع وحلول التربية الإسلامية لشهادة التعليم المتوسط', 'fas fa-mosque', '#16a085'),
(4, 'اللغة الفرنسية', 'مواضيع وحلول اللغة الفرنسية لشهادة التعليم المتوسط', 'fas fa-language', '#3498db'),
(4, 'اللغة الإنجليزية', 'مواضيع وحلول اللغة الإنجليزية لشهادة التعليم المتوسط', 'fas fa-language', '#e67e22');

-- إدراج المواد الدراسية لشهادة البكالوريا
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
-- شعبة العلوم التجريبية
(5, 'الرياضيات - علوم تجريبية', 'مواضيع وحلول الرياضيات لشعبة العلوم التجريبية', 'fas fa-calculator', '#e74c3c'),
(5, 'الفيزياء - علوم تجريبية', 'مواضيع وحلول الفيزياء لشعبة العلوم التجريبية', 'fas fa-atom', '#9b59b6'),
(5, 'علوم الطبيعة والحياة - علوم تجريبية', 'مواضيع وحلول علوم الطبيعة والحياة لشعبة العلوم التجريبية', 'fas fa-dna', '#27ae60'),
-- شعبة الرياضيات
(5, 'الرياضيات - رياضيات', 'مواضيع وحلول الرياضيات لشعبة الرياضيات', 'fas fa-calculator', '#c0392b'),
(5, 'الفيزياء - رياضيات', 'مواضيع وحلول الفيزياء لشعبة الرياضيات', 'fas fa-atom', '#8e44ad'),
-- شعبة تقني رياضي
(5, 'الرياضيات - تقني رياضي', 'مواضيع وحلول الرياضيات لشعبة تقني رياضي', 'fas fa-calculator', '#d35400'),
(5, 'الفيزياء - تقني رياضي', 'مواضيع وحلول الفيزياء لشعبة تقني رياضي', 'fas fa-atom', '#7f8c8d'),
(5, 'الهندسة المدنية', 'مواضيع وحلول الهندسة المدنية لشعبة تقني رياضي', 'fas fa-building', '#34495e'),
(5, 'الهندسة الميكانيكية', 'مواضيع وحلول الهندسة الميكانيكية لشعبة تقني رياضي', 'fas fa-cogs', '#95a5a6'),
(5, 'الهندسة الكهربائية', 'مواضيع وحلول الهندسة الكهربائية لشعبة تقني رياضي', 'fas fa-bolt', '#f1c40f'),
-- شعبة الآداب والفلسفة
(5, 'الفلسفة - آداب وفلسفة', 'مواضيع وحلول الفلسفة لشعبة الآداب والفلسفة', 'fas fa-brain', '#34495e'),
(5, 'اللغة العربية - آداب وفلسفة', 'مواضيع وحلول اللغة العربية لشعبة الآداب والفلسفة', 'fas fa-book-open', '#2ecc71'),
(5, 'التاريخ والجغرافيا - آداب وفلسفة', 'مواضيع وحلول التاريخ والجغرافيا لشعبة الآداب والفلسفة', 'fas fa-globe-americas', '#f39c12'),
-- شعبة اللغات الأجنبية
(5, 'اللغة الإنجليزية - لغات أجنبية', 'مواضيع وحلول اللغة الإنجليزية لشعبة اللغات الأجنبية', 'fas fa-language', '#e67e22'),
(5, 'اللغة الفرنسية - لغات أجنبية', 'مواضيع وحلول اللغة الفرنسية لشعبة اللغات الأجنبية', 'fas fa-language', '#3498db'),
(5, 'اللغة الألمانية - لغات أجنبية', 'مواضيع وحلول اللغة الألمانية لشعبة اللغات الأجنبية', 'fas fa-language', '#e74c3c'),
(5, 'اللغة الإسبانية - لغات أجنبية', 'مواضيع وحلول اللغة الإسبانية لشعبة اللغات الأجنبية', 'fas fa-language', '#f39c12'),
-- مواد مشتركة للبكالوريا
(5, 'اللغة العربية - مشترك', 'مواضيع وحلول اللغة العربية المشتركة لجميع الشعب', 'fas fa-book-open', '#2ecc71'),
(5, 'التربية الإسلامية - مشترك', 'مواضيع وحلول التربية الإسلامية المشتركة لجميع الشعب', 'fas fa-mosque', '#16a085'),
(5, 'اللغة الفرنسية - مشترك', 'مواضيع وحلول اللغة الفرنسية المشتركة لجميع الشعب', 'fas fa-language', '#3498db'),
(5, 'اللغة الإنجليزية - مشترك', 'مواضيع وحلول اللغة الإنجليزية المشتركة لجميع الشعب', 'fas fa-language', '#e67e22');

-- إدراج السنوات الدراسية لشهادة التعليم المتوسط
INSERT INTO exam_years (year, level_id, description) VALUES
('2024', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2024'),
('2023', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2023'),
('2022', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2022'),
('2021', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2021'),
('2020', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2020'),
('2019', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2019'),
('2018', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2018'),
('2017', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2017'),
('2016', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2016'),
('2015', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2015');

-- إدراج السنوات الدراسية لشهادة البكالوريا
INSERT INTO exam_years (year, level_id, description) VALUES
('2024', 5, 'مواضيع وحلول شهادة البكالوريا 2024'),
('2023', 5, 'مواضيع وحلول شهادة البكالوريا 2023'),
('2022', 5, 'مواضيع وحلول شهادة البكالوريا 2022'),
('2021', 5, 'مواضيع وحلول شهادة البكالوريا 2021'),
('2020', 5, 'مواضيع وحلول شهادة البكالوريا 2020'),
('2019', 5, 'مواضيع وحلول شهادة البكالوريا 2019'),
('2018', 5, 'مواضيع وحلول شهادة البكالوريا 2018'),
('2017', 5, 'مواضيع وحلول شهادة البكالوريا 2017'),
('2016', 5, 'مواضيع وحلول شهادة البكالوريا 2016'),
('2015', 5, 'مواضيع وحلول شهادة البكالوريا 2015'),
('2014', 5, 'مواضيع وحلول شهادة البكالوريا 2014'),
('2013', 5, 'مواضيع وحلول شهادة البكالوريا 2013'),
('2012', 5, 'مواضيع وحلول شهادة البكالوريا 2012'),
('2011', 5, 'مواضيع وحلول شهادة البكالوريا 2011'),
('2010', 5, 'مواضيع وحلول شهادة البكالوريا 2010');

-- إنشاء مستخدم مدير افتراضي
INSERT INTO users (username, email, password, full_name, role, is_active, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE, TRUE);

-- إدراج بعض الإعدادات الافتراضية
INSERT INTO settings (setting_key, setting_value, description) VALUES
('site_name', 'منصة التعليم', 'اسم الموقع'),
('site_description', 'منصة تعليمية شاملة للدروس والامتحانات', 'وصف الموقع'),
('admin_email', '<EMAIL>', 'بريد المدير الإلكتروني'),
('max_file_size', '10485760', 'الحد الأقصى لحجم الملف بالبايت (10MB)'),
('enable_registration', '1', 'تفعيل التسجيل الجديد'),
('enable_comments', '1', 'تفعيل التعليقات'),
('maintenance_mode', '0', 'وضع الصيانة'),
('items_per_page', '12', 'عدد العناصر في الصفحة الواحدة');

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_documents_featured ON documents(is_featured, upload_date);
CREATE INDEX idx_documents_approved ON documents(is_approved, upload_date);
CREATE INDEX idx_users_role ON users(role, is_active);
CREATE INDEX idx_subjects_level ON subjects(level_id, name);
