<?php
session_start();
require_once '../db/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// معالجة حذف الملف
if (isset($_POST['delete_document'])) {
    $documentId = intval($_POST['document_id']);
    
    try {
        // جلب معلومات الملف
        $stmt = $pdo->prepare("SELECT file_path, title FROM documents WHERE id = ?");
        $stmt->execute([$documentId]);
        $document = $stmt->fetch();
        
        if ($document) {
            // حذف الملف من الخادم
            if (file_exists('../' . $document['file_path'])) {
                unlink('../' . $document['file_path']);
            }
            
            // حذف السجل من قاعدة البيانات
            $stmt = $pdo->prepare("DELETE FROM documents WHERE id = ?");
            if ($stmt->execute([$documentId])) {
                logActivity($_SESSION['user_id'], 'delete', "حذف الملف: " . $document['title']);
                $success = 'تم حذف الملف بنجاح!';
            } else {
                $error = 'حدث خطأ أثناء حذف الملف';
            }
        } else {
            $error = 'الملف غير موجود';
        }
    } catch (Exception $e) {
        $error = 'حدث خطأ: ' . $e->getMessage();
    }
}

// معالجة البحث والفلترة
$search = $_GET['search'] ?? '';
$level_filter = $_GET['level'] ?? '';
$subject_filter = $_GET['subject'] ?? '';
$type_filter = $_GET['type'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// بناء استعلام البحث
$sql = "SELECT d.*, s.name as subject_name, l.name as level_name, u.full_name as uploader_name
        FROM documents d
        JOIN subjects s ON d.subject_id = s.id
        JOIN levels l ON s.level_id = l.id
        JOIN users u ON d.uploaded_by = u.id
        WHERE 1=1";

$params = [];

if ($search) {
    $sql .= " AND (d.title LIKE ? OR d.description LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if ($level_filter) {
    $sql .= " AND l.id = ?";
    $params[] = $level_filter;
}

if ($subject_filter) {
    $sql .= " AND s.id = ?";
    $params[] = $subject_filter;
}

if ($type_filter) {
    $sql .= " AND d.type = ?";
    $params[] = $type_filter;
}

$sql .= " ORDER BY d.upload_date DESC LIMIT $limit OFFSET $offset";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$documents = $stmt->fetchAll();

// حساب إجمالي النتائج
$countSql = str_replace("SELECT d.*, s.name as subject_name, l.name as level_name, u.full_name as uploader_name", "SELECT COUNT(*)", $sql);
$countSql = str_replace("ORDER BY d.upload_date DESC LIMIT $limit OFFSET $offset", "", $countSql);
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalDocuments = $countStmt->fetchColumn();
$totalPages = ceil($totalDocuments / $limit);

// جلب البيانات للفلاتر
$levels = getLevels();
$subjects = [];
if ($level_filter) {
    $subjects = getSubjectsByLevel($level_filter);
}

// جلب إحصائيات سريعة
$stats = [
    'total_documents' => $pdo->query("SELECT COUNT(*) FROM documents")->fetchColumn(),
    'total_downloads' => $pdo->query("SELECT SUM(download_count) FROM documents")->fetchColumn(),
    'total_views' => $pdo->query("SELECT SUM(view_count) FROM documents")->fetchColumn(),
    'recent_uploads' => $pdo->query("SELECT COUNT(*) FROM documents WHERE upload_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)")->fetchColumn()
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الملفات - لوحة التحكم</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: #fff;
        }

        .sidebar-menu i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            margin-right: 250px;
            padding: 2rem;
        }

        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .page-header h1 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
            font-size: 2rem;
        }

        .page-header p {
            margin: 0;
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #3498db;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9rem;
        }

        .filters-section {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .filter-input {
            padding: 0.75rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .filter-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
        }

        .documents-section {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            padding: 1.5rem 2rem;
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            border-bottom: 1px solid #e1e8ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .documents-table {
            width: 100%;
            border-collapse: collapse;
        }

        .documents-table th,
        .documents-table td {
            padding: 1rem;
            text-align: right;
            border-bottom: 1px solid #e1e8ed;
        }

        .documents-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .documents-table tr:hover {
            background: #f8f9fa;
        }

        .document-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .document-meta {
            font-size: 0.85rem;
            color: #666;
        }

        .type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .type-درس { background: #e3f2fd; color: #1976d2; }
        .type-امتحان { background: #fff3e0; color: #f57c00; }
        .type-حل { background: #e8f5e8; color: #388e3c; }
        .type-تمرين { background: #fce4ec; color: #c2185b; }
        .type-ملخص { background: #f3e5f5; color: #7b1fa2; }

        .stats-mini {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
            color: #666;
        }

        .stats-mini span {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            padding: 2rem;
        }

        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #e1e8ed;
            border-radius: 5px;
            text-decoration: none;
            color: #2c3e50;
        }

        .pagination a:hover {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .pagination .current {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .main-content {
                margin-right: 0;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .documents-table {
                font-size: 0.9rem;
            }

            .actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-graduation-cap"></i> لوحة التحكم</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="documents.php" class="active"><i class="fas fa-file-alt"></i> إدارة الملفات</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subjects.php"><i class="fas fa-book"></i> إدارة المواد</a></li>
                <li><a href="comments.php"><i class="fas fa-comments"></i> التعليقات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../index.php"><i class="fas fa-arrow-right"></i> العودة للموقع</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-file-alt"></i> إدارة الملفات</h1>
                <p>إدارة وتنظيم جميع الملفات والوثائق في المنصة</p>
            </div>

            <!-- Alerts -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?= $success ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?= $error ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-file-alt"></i></div>
                    <div class="number"><?= number_format($stats['total_documents']) ?></div>
                    <div class="label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-download"></i></div>
                    <div class="number"><?= number_format($stats['total_downloads']) ?></div>
                    <div class="label">إجمالي التحميلات</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-eye"></i></div>
                    <div class="number"><?= number_format($stats['total_views']) ?></div>
                    <div class="label">إجمالي المشاهدات</div>
                </div>
                <div class="stat-card">
                    <div class="icon"><i class="fas fa-clock"></i></div>
                    <div class="number"><?= number_format($stats['recent_uploads']) ?></div>
                    <div class="label">رفع هذا الأسبوع</div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filters-section">
                <form method="GET" action="">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label for="search">البحث</label>
                            <input type="text" id="search" name="search" class="filter-input"
                                   value="<?= htmlspecialchars($search) ?>" placeholder="ابحث في العناوين والأوصاف...">
                        </div>

                        <div class="filter-group">
                            <label for="level">المستوى</label>
                            <select id="level" name="level" class="filter-input" onchange="loadSubjects(this.value)">
                                <option value="">جميع المستويات</option>
                                <?php foreach($levels as $level): ?>
                                    <option value="<?= $level['id'] ?>" <?= $level_filter == $level['id'] ? 'selected' : '' ?>>
                                        <?= $level['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="subject">المادة</label>
                            <select id="subject" name="subject" class="filter-input">
                                <option value="">جميع المواد</option>
                                <?php foreach($subjects as $subject): ?>
                                    <option value="<?= $subject['id'] ?>" <?= $subject_filter == $subject['id'] ? 'selected' : '' ?>>
                                        <?= $subject['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="type">النوع</label>
                            <select id="type" name="type" class="filter-input">
                                <option value="">جميع الأنواع</option>
                                <option value="درس" <?= $type_filter == 'درس' ? 'selected' : '' ?>>درس</option>
                                <option value="امتحان" <?= $type_filter == 'امتحان' ? 'selected' : '' ?>>امتحان</option>
                                <option value="حل" <?= $type_filter == 'حل' ? 'selected' : '' ?>>حل</option>
                                <option value="تمرين" <?= $type_filter == 'تمرين' ? 'selected' : '' ?>>تمرين</option>
                                <option value="ملخص" <?= $type_filter == 'ملخص' ? 'selected' : '' ?>>ملخص</option>
                                <option value="موضوع شهادة" <?= $type_filter == 'موضوع شهادة' ? 'selected' : '' ?>>موضوع شهادة</option>
                                <option value="حل شهادة" <?= $type_filter == 'حل شهادة' ? 'selected' : '' ?>>حل شهادة</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Documents Section -->
            <div class="documents-section">
                <div class="section-header">
                    <h3><i class="fas fa-list"></i> قائمة الملفات (<?= number_format($totalDocuments) ?> ملف)</h3>
                    <a href="add_document.php" class="btn btn-success">
                        <i class="fas fa-plus"></i> إضافة ملف جديد
                    </a>
                </div>

                <?php if (empty($documents)): ?>
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <i class="fas fa-folder-open" style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h3>لا توجد ملفات</h3>
                        <p>لم يتم العثور على أي ملفات تطابق معايير البحث</p>
                        <a href="add_document.php" class="btn btn-primary">إضافة أول ملف</a>
                    </div>
                <?php else: ?>
                    <table class="documents-table">
                        <thead>
                            <tr>
                                <th>الملف</th>
                                <th>النوع</th>
                                <th>المادة</th>
                                <th>المستوى</th>
                                <th>الرافع</th>
                                <th>الإحصائيات</th>
                                <th>تاريخ الرفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($documents as $doc): ?>
                                <tr>
                                    <td>
                                        <div class="document-title"><?= htmlspecialchars($doc['title']) ?></div>
                                        <?php if($doc['description']): ?>
                                            <div class="document-meta"><?= htmlspecialchars(substr($doc['description'], 0, 100)) ?>...</div>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="type-badge type-<?= str_replace(' ', '', $doc['type']) ?>">
                                            <?= $doc['type'] ?>
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($doc['subject_name']) ?></td>
                                    <td><?= htmlspecialchars($doc['level_name']) ?></td>
                                    <td><?= htmlspecialchars($doc['uploader_name']) ?></td>
                                    <td>
                                        <div class="stats-mini">
                                            <span><i class="fas fa-download"></i> <?= number_format($doc['download_count']) ?></span>
                                            <span><i class="fas fa-eye"></i> <?= number_format($doc['view_count']) ?></span>
                                        </div>
                                    </td>
                                    <td><?= date('Y-m-d', strtotime($doc['upload_date'])) ?></td>
                                    <td>
                                        <div class="actions">
                                            <a href="../view_document.php?id=<?= $doc['id'] ?>" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-eye"></i> عرض
                                            </a>
                                            <a href="../download.php?id=<?= $doc['id'] ?>" class="btn btn-success btn-sm">
                                                <i class="fas fa-download"></i> تحميل
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete(<?= $doc['id'] ?>, '<?= htmlspecialchars($doc['title']) ?>')">
                                                <i class="fas fa-trash"></i> حذف
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?= $i ?></span>
                                <?php else: ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
        <div style="background: white; padding: 2rem; border-radius: 15px; max-width: 400px; text-align: center;">
            <h3 style="color: #e74c3c; margin-bottom: 1rem;">
                <i class="fas fa-exclamation-triangle"></i> تأكيد الحذف
            </h3>
            <p id="deleteMessage" style="margin-bottom: 2rem; color: #666;"></p>
            <form method="POST" action="" style="display: inline;">
                <input type="hidden" name="document_id" id="deleteDocumentId">
                <button type="button" onclick="closeDeleteModal()" class="btn" style="background: #95a5a6; color: white; margin-left: 1rem;">
                    إلغاء
                </button>
                <button type="submit" name="delete_document" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف نهائياً
                </button>
            </form>
        </div>
    </div>

    <script>
        // تحميل المواد حسب المستوى
        function loadSubjects(levelId) {
            const subjectSelect = document.getElementById('subject');
            subjectSelect.innerHTML = '<option value="">جميع المواد</option>';

            if (levelId) {
                fetch(`../ajax/get_subjects.php?level_id=${levelId}`)
                    .then(response => response.json())
                    .then(subjects => {
                        subjects.forEach(subject => {
                            const option = document.createElement('option');
                            option.value = subject.id;
                            option.textContent = subject.name;
                            if (subject.id == '<?= $subject_filter ?>') {
                                option.selected = true;
                            }
                            subjectSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error:', error));
            }
        }

        // تأكيد الحذف
        function confirmDelete(documentId, documentTitle) {
            document.getElementById('deleteDocumentId').value = documentId;
            document.getElementById('deleteMessage').textContent = `هل أنت متأكد من حذف الملف "${documentTitle}"؟ هذا الإجراء لا يمكن التراجع عنه.`;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        // إغلاق نافذة الحذف
        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        // إغلاق النافذة عند النقر خارجها
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });

        // تحميل المواد عند تحميل الصفحة إذا كان هناك مستوى محدد
        document.addEventListener('DOMContentLoaded', function() {
            const levelSelect = document.getElementById('level');
            if (levelSelect.value) {
                loadSubjects(levelSelect.value);
            }
        });
    </script>
</body>
</html>
