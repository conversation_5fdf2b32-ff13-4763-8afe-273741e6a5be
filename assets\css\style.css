/* متغيرات CSS للألوان والتأثيرات */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --accent-color: #f39c12;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;

    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #2c3e50, #3498db);
    --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 15px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 25px rgba(0,0,0,0.2);
    --shadow-glow: 0 0 20px rgba(52, 152, 219, 0.3);

    --border-radius: 15px;
    --border-radius-small: 8px;
    --border-radius-large: 25px;

    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    direction: rtl;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
}

/* حاويات مخصصة للأقسام المختلفة */
.levels-section .container,
.latest-content .container {
    max-width: 1300px;
}

.stats-section .container {
    max-width: 1100px;
}

/* تحسينات التجاوب للحاوي */
@media (max-width: 1400px) {
    .container {
        max-width: 1200px;
        padding: 0 1.5rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }

    .levels-section .container,
    .latest-content .container,
    .stats-section .container {
        max-width: 100%;
    }
}

/* Header - تحسينات متقدمة */
.header {
    background: var(--gradient-secondary);
    color: white;
    padding: 1rem 0;
    box-shadow: var(--shadow-medium);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: all var(--transition-normal);
}

.header.scrolled {
    padding: 0.5rem 0;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(20px);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-brand i {
    color: #f39c12;
    margin-left: 10px;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0.7rem 1.2rem;
    border-radius: var(--border-radius-small);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
    font-weight: 500;
}

.nav-menu a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.nav-menu a:hover::before {
    left: 100%;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: rgba(255,255,255,0.25);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.nav-menu a.active {
    background: rgba(255,255,255,0.3);
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    flex-direction: column;
    padding: 0.5rem 0;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    color: #333;
    padding: 0.7rem 1rem;
    border-radius: 0;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.search-box {
    max-width: 600px;
    margin: 0 auto;
}

.search-input-group {
    display: flex;
    background: rgba(255,255,255,0.95);
    border-radius: 50px;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
    transition: all var(--transition-normal);
}

.search-input-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.search-input-group:focus-within {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0,0,0,0.25), var(--shadow-glow);
}

.search-input-group input {
    flex: 1;
    padding: 1.2rem 1.8rem;
    border: none;
    outline: none;
    font-size: 1.1rem;
    background: transparent;
    color: #333;
    font-weight: 500;
}

.search-input-group input::placeholder {
    color: #999;
    font-style: italic;
}

.search-input-group select {
    padding: 1.2rem 1rem;
    border: none;
    outline: none;
    background: rgba(248,249,250,0.8);
    color: #333;
    font-weight: 500;
    cursor: pointer;
    transition: background var(--transition-normal);
}

.search-input-group select:hover {
    background: rgba(248,249,250,1);
}

.search-input-group button {
    padding: 1.2rem 2rem;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.search-input-group button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
}

.search-input-group button:hover::before {
    width: 300px;
    height: 300px;
}

.search-input-group button:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: scale(1.05);
}

/* Sections - تحسينات التباعد */
.levels-section,
.latest-content,
.stats-section {
    padding: 5rem 0;
    position: relative;
}

.levels-section::before,
.latest-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.3), transparent);
}

/* تحسين المسافات بين الأقسام */
.levels-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.latest-content {
    background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
}

.levels-section h3,
.latest-content h3 {
    text-align: center;
    font-size: 2.8rem;
    margin-bottom: 4rem;
    color: #2c3e50;
    position: relative;
    font-weight: 700;
    letter-spacing: 1px;
}

.levels-section h3::after,
.latest-content h3::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 2px;
}

.levels-section h3 i,
.latest-content h3 i {
    color: #3498db;
    margin-left: 15px;
    font-size: 2.5rem;
    vertical-align: middle;
}

/* Grids - تحسينات التخطيط */
.levels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    align-items: stretch;
    justify-content: center;
    max-width: 1400px;
    margin: 0 auto;
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    align-items: stretch;
    justify-content: center;
    max-width: 1400px;
    margin: 0 auto;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    align-items: stretch;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

/* تحسينات إضافية للشبكة */
@media (min-width: 1200px) {
    .levels-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
    }

    .content-grid {
        grid-template-columns: repeat(3, 1fr);
        max-width: 1200px;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        max-width: 1000px;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .levels-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .content-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

/* Cards - تحسينات متقدمة */
.level-card,
.content-card,
.stat-card {
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 300px;
    height: 100%;
}

.level-card::before,
.content-card::before,
.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.level-card:hover::before,
.content-card:hover::before,
.stat-card:hover::before {
    transform: scaleX(1);
}

.level-card:hover,
.content-card:hover,
.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy), var(--shadow-glow);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
}

/* تأثيرات خاصة للبطاقات */
.level-card {
    cursor: pointer;
    transition: all var(--transition-normal), background-color 0.6s ease;
}

.level-card:nth-child(1) { --card-color: #3498db; }
.level-card:nth-child(2) { --card-color: #e74c3c; }
.level-card:nth-child(3) { --card-color: #f39c12; }
.level-card:nth-child(4) { --card-color: #27ae60; }
.level-card:nth-child(5) { --card-color: #9b59b6; }
.level-card:nth-child(6) { --card-color: #1abc9c; }

.level-card:hover {
    background: linear-gradient(145deg, var(--card-color), rgba(255,255,255,0.9));
    color: white;
}

.level-card:hover .level-icon {
    color: white;
    transform: scale(1.2) rotate(10deg);
    text-shadow: 0 0 20px rgba(255,255,255,0.5);
}

/* تحسينات خاصة لبطاقات المحتوى */
.content-card {
    min-height: 350px;
}

.content-card .content-type {
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.content-card h4 {
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    min-height: 60px;
}

.content-card .content-meta {
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.content-card .content-description {
    flex-grow: 1;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 60px;
}

.content-card .content-actions {
    margin-top: auto;
    margin-bottom: 1rem;
    flex-shrink: 0;
}

.content-card .content-date {
    flex-shrink: 0;
}

/* تحسينات خاصة لبطاقات المستويات */
.level-card {
    min-height: 280px;
}

.level-card h4 {
    margin: 1rem 0;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50px;
}

.level-card p {
    flex-grow: 1;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.level-card .btn {
    margin-top: auto;
}

/* تحسينات خاصة لبطاقات الإحصائيات */
.stat-card {
    min-height: 200px;
    justify-content: center;
}

.stat-card .stat-icon {
    flex-shrink: 0;
}

.stat-card .stat-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* تحسينات الأيقونات */
.level-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    transition: all var(--transition-normal);
    display: inline-block;
    position: relative;
    animation: float 3s ease-in-out infinite;
}

.level-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.2), transparent);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: transform var(--transition-normal);
    z-index: -1;
}

.level-card:hover .level-icon::after {
    transform: translate(-50%, -50%) scale(1);
}

/* أنيميشن الطفو */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* أنيميشن النبض */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* أنيميشن التوهج */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(52, 152, 219, 0.5); }
    50% { box-shadow: 0 0 20px rgba(52, 152, 219, 0.8), 0 0 30px rgba(52, 152, 219, 0.6); }
}

.level-card h4,
.content-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.content-type {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #3498db;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.content-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.content-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.content-description {
    color: #666;
    margin-bottom: 1.5rem;
    text-align: right;
}

.content-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.content-date {
    font-size: 0.8rem;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
}

/* Buttons - تحسينات متقدمة */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.8rem 1.8rem;
    border-radius: var(--border-radius-large);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    text-align: center;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1f5f8b, #2980b9);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-heavy), 0 0 20px rgba(52, 152, 219, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    position: relative;
}

.btn-outline::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-color);
    transition: width var(--transition-normal);
    z-index: -1;
}

.btn-outline:hover::after {
    width: 100%;
}

.btn-outline:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* أزرار متخصصة */
.btn-success {
    background: var(--gradient-success);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

/* Stats Section - تحسينات متقدمة */
.stats-section {
    background: var(--gradient-primary);
    color: white;
    position: relative;
    overflow: hidden;
}

.stats-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

.stat-card {
    background: rgba(255,255,255,0.15);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

.stat-card:hover::before {
    transform: scale(1);
}

.stat-icon {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
    transition: all var(--transition-normal);
    animation: pulse 2s ease-in-out infinite;
}

.stat-card:hover .stat-icon {
    transform: scale(1.2) rotate(5deg);
    color: #fff;
    text-shadow: 0 0 20px rgba(255,255,255,0.8);
}

.stat-content h4 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
    color: white;
    font-weight: 700;
    transition: all var(--transition-normal);
    counter-reset: number;
}

.stat-content h4.animate-number {
    animation: countUp 2s ease-out;
}

@keyframes countUp {
    from { transform: scale(0.5); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.stat-content p {
    font-size: 1.2rem;
    opacity: 0.95;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #3498db;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #3498db;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #2980b9;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Additional Styles - تحسينات متقدمة */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1.2rem 1.8rem;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 600;
    z-index: 10000;
    transform: translateX(400px) scale(0.8);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-heavy);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    min-width: 300px;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.message.show {
    transform: translateX(0) scale(1);
    animation: messageSlide 0.5s ease-out;
}

@keyframes messageSlide {
    0% {
        transform: translateX(400px) scale(0.8) rotate(10deg);
        opacity: 0;
    }
    50% {
        transform: translateX(-20px) scale(1.05) rotate(-2deg);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0) scale(1) rotate(0deg);
        opacity: 1;
    }
}

.message-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-left: 4px solid #2980b9;
}
.message-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    border-left: 4px solid #229954;
}
.message-error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-left: 4px solid #c0392b;
}
.message-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-left: 4px solid #e67e22;
}

.message-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-right: 1rem;
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dark-mode {
    background-color: #1a1a1a;
    color: #e1e1e1;
}

.dark-mode .header {
    background: linear-gradient(135deg, #1a1a1a, #2c3e50);
}

.dark-mode .level-card,
.dark-mode .content-card,
.dark-mode .stat-card {
    background: #2c3e50;
    color: #e1e1e1;
}

/* Responsive Design - تحسينات متقدمة */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(44, 62, 80, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        padding: 1.5rem;
        box-shadow: var(--shadow-heavy);
        border-top: 1px solid rgba(255,255,255,0.1);
        transform: translateY(-20px);
        opacity: 0;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        display: flex;
        transform: translateY(0);
        opacity: 1;
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0;
    }

    .nav-menu li {
        width: 100%;
    }

    .nav-menu a {
        padding: 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .search-input-group {
        flex-direction: column;
        border-radius: 10px;
    }

    .search-input-group input,
    .search-input-group select,
    .search-input-group button {
        border-radius: 0;
    }

    .content-actions {
        flex-direction: column;
    }

    .content-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .container {
        padding: 0 15px;
    }

    /* تحسينات إضافية للموبايل */
    .levels-grid,
    .content-grid,
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .level-card,
    .content-card,
    .stat-card {
        margin-bottom: 1rem;
        transform: none;
        min-height: auto;
        padding: 1.5rem;
    }

    .level-card:hover,
    .content-card:hover,
    .stat-card:hover {
        transform: translateY(-5px) scale(1.02);
    }

    .content-card {
        min-height: 300px;
    }

    .level-card {
        min-height: 250px;
    }

    .stat-card {
        min-height: 150px;
    }

    .content-card h4,
    .level-card h4 {
        min-height: 40px;
        font-size: 1.3rem;
    }

    .content-card .content-description,
    .level-card p {
        -webkit-line-clamp: 2;
        min-height: 40px;
    }

    .message {
        right: 10px;
        left: 10px;
        min-width: auto;
        transform: translateY(-100px);
    }

    .message.show {
        transform: translateY(0);
    }
}

/* تحسينات إضافية للتفاعل */
.interactive-element {
    transition: all var(--transition-normal);
}

.interactive-element:hover {
    transform: translateY(-2px);
}

/* تأثيرات الظهور التدريجي */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: all 0.6s ease;
}

.fade-in-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.6s ease;
}

.fade-in-right.visible {
    opacity: 1;
    transform: translateX(0);
}

/* تأثيرات التحميل */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تحسينات الأداء */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* تحسينات إضافية للتنظيم */
.section-spacing {
    margin-bottom: 3rem;
}

.card-container {
    width: 100%;
    max-width: 100%;
}

/* تحسين التباعد بين العناصر */
.element-spacing > * + * {
    margin-top: 1rem;
}

/* تحسين الانتقالات */
.smooth-transition {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* تحسين الظلال */
.enhanced-shadow {
    box-shadow:
        0 2px 4px rgba(0,0,0,0.1),
        0 8px 16px rgba(0,0,0,0.1),
        0 16px 32px rgba(0,0,0,0.1);
}

/* تحسين الحدود */
.enhanced-border {
    border: 1px solid rgba(255,255,255,0.2);
    backdrop-filter: blur(10px);
}

/* تحسين النصوص */
.enhanced-text {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* تأثيرات خاصة للعناصر التفاعلية */
.ripple {
    position: relative;
    overflow: hidden;
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255,255,255,0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}
