/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h1 {
    font-size: 1.8rem;
    font-weight: bold;
}

.nav-brand i {
    color: #f39c12;
    margin-left: 10px;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-menu a:hover,
.nav-menu a.active {
    background-color: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    border-radius: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    flex-direction: column;
    padding: 0.5rem 0;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu a {
    color: #333;
    padding: 0.7rem 1rem;
    border-radius: 0;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-content h2 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.search-box {
    max-width: 600px;
    margin: 0 auto;
}

.search-input-group {
    display: flex;
    background: white;
    border-radius: 50px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.search-input-group input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    outline: none;
    font-size: 1rem;
}

.search-input-group select {
    padding: 1rem;
    border: none;
    outline: none;
    background: #f8f9fa;
    color: #333;
}

.search-input-group button {
    padding: 1rem 1.5rem;
    background: #e74c3c;
    color: white;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.search-input-group button:hover {
    background: #c0392b;
}

/* Sections */
.levels-section,
.latest-content,
.stats-section {
    padding: 4rem 0;
}

.levels-section h3,
.latest-content h3 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #2c3e50;
}

.levels-section h3 i,
.latest-content h3 i {
    color: #3498db;
    margin-left: 10px;
}

/* Grids */
.levels-grid,
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

/* Cards */
.level-card,
.content-card,
.stat-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.level-card:hover,
.content-card:hover,
.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.level-icon {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.level-card h4,
.content-card h4 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.content-type {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: #3498db;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.content-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #666;
}

.content-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.content-description {
    color: #666;
    margin-bottom: 1.5rem;
    text-align: right;
}

.content-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.content-date {
    font-size: 0.8rem;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.7rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #3498db;
    border: 2px solid #3498db;
}

.btn-outline:hover {
    background: #3498db;
    color: white;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.stat-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #f39c12;
}

.stat-content h4 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: white;
}

.stat-content p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    margin-bottom: 1rem;
    color: #3498db;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #3498db;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #2980b9;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Additional Styles */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.message.show {
    transform: translateX(0);
}

.message-info { background: #3498db; }
.message-success { background: #27ae60; }
.message-error { background: #e74c3c; }
.message-warning { background: #f39c12; }

.message-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-right: 1rem;
}

.loading {
    opacity: 0.7;
    pointer-events: none;
}

.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dark-mode {
    background-color: #1a1a1a;
    color: #e1e1e1;
}

.dark-mode .header {
    background: linear-gradient(135deg, #1a1a1a, #2c3e50);
}

.dark-mode .level-card,
.dark-mode .content-card,
.dark-mode .stat-card {
    background: #2c3e50;
    color: #e1e1e1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #2c3e50;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0;
    }

    .nav-menu li {
        width: 100%;
    }

    .nav-menu a {
        padding: 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .search-input-group {
        flex-direction: column;
        border-radius: 10px;
    }

    .search-input-group input,
    .search-input-group select,
    .search-input-group button {
        border-radius: 0;
    }

    .content-actions {
        flex-direction: column;
    }

    .content-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .container {
        padding: 0 15px;
    }
}
