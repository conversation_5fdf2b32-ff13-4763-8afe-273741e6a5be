<?php
session_start();
require_once 'db/config.php';

// إعادة توجيه المستخدم المسجل دخوله بالفعل
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $full_name = sanitize($_POST['full_name'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $role = sanitize($_POST['role'] ?? 'student');
    $terms = isset($_POST['terms']);
    
    // التحقق من صحة البيانات
    if (empty($username) || empty($email) || empty($full_name) || empty($password) || empty($confirm_password)) {
        $error = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (strlen($username) < 3) {
        $error = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
    } elseif (!validateEmail($email)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } elseif (strlen($password) < 6) {
        $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور وتأكيدها غير متطابقتين';
    } elseif (!$terms) {
        $error = 'يجب الموافقة على الشروط والأحكام';
    } else {
        try {
            // التحقق من عدم وجود المستخدم مسبقاً
            $checkStmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $checkStmt->execute([$username, $email]);
            
            if ($checkStmt->fetch()) {
                $error = 'اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل';
            } else {
                // إنشاء المستخدم الجديد
                $hashedPassword = hashPassword($password);
                $verificationToken = bin2hex(random_bytes(32));
                
                $stmt = $pdo->prepare("
                    INSERT INTO users (username, email, password, full_name, role, verification_token, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())
                ");
                
                if ($stmt->execute([$username, $email, $hashedPassword, $full_name, $role, $verificationToken])) {
                    $userId = $pdo->lastInsertId();
                    
                    // تسجيل النشاط
                    logActivity($userId, 'register', 'تسجيل مستخدم جديد');
                    
                    // إرسال بريد التحقق (يمكن تطويره لاحقاً)
                    // sendVerificationEmail($email, $verificationToken);
                    
                    $success = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                    
                    // إعادة توجيه إلى صفحة تسجيل الدخول
                    header("refresh:3;url=login.php");
                } else {
                    $error = 'حدث خطأ أثناء إنشاء الحساب';
                }
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
            error_log($e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
        }
        
        .auth-card {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .auth-header {
            margin-bottom: 2rem;
        }
        
        .auth-header h1 {
            color: #2c3e50;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .auth-header p {
            color: #666;
            font-size: 1rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: bold;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .form-input.error, .form-select.error {
            border-color: #e74c3c;
        }
        
        .password-strength {
            margin-top: 0.5rem;
            font-size: 0.9rem;
        }
        
        .strength-weak { color: #e74c3c; }
        .strength-medium { color: #f39c12; }
        .strength-strong { color: #27ae60; }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 1.5rem;
            text-align: right;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-top: 0.2rem;
        }
        
        .checkbox-group label {
            margin-bottom: 0;
            font-weight: normal;
            line-height: 1.4;
        }
        
        .btn-auth {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .btn-auth:hover {
            background: linear-gradient(135deg, #229954, #27ae60);
            transform: translateY(-2px);
        }
        
        .auth-links {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e1e8ed;
        }
        
        .auth-links a {
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
            transition: color 0.3s ease;
        }
        
        .auth-links a:hover {
            color: #2980b9;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .alert-error {
            background: #fee;
            color: #c0392b;
            border: 1px solid #f5b7b1;
        }
        
        .alert-success {
            background: #eafaf1;
            color: #27ae60;
            border: 1px solid #a9dfbf;
        }
        
        .back-home {
            position: absolute;
            top: 2rem;
            right: 2rem;
            color: white;
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .back-home:hover {
            transform: translateX(-5px);
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .auth-card {
                padding: 2rem;
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.php" class="back-home">
        <i class="fas fa-arrow-right"></i> العودة للرئيسية
    </a>
    
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1><i class="fas fa-user-plus"></i> إنشاء حساب جديد</h1>
                <p>انضم إلى منصة التعليم واستفد من المحتوى التعليمي</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?= $success ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" id="registerForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="username">اسم المستخدم *</label>
                        <input type="text" id="username" name="username" class="form-input" 
                               value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" 
                               placeholder="اختر اسم مستخدم" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="full_name">الاسم الكامل *</label>
                        <input type="text" id="full_name" name="full_name" class="form-input" 
                               value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>" 
                               placeholder="أدخل اسمك الكامل" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني *</label>
                    <input type="email" id="email" name="email" class="form-input" 
                           value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" 
                           placeholder="أدخل بريدك الإلكتروني" required>
                </div>
                
                <div class="form-group">
                    <label for="role">نوع الحساب</label>
                    <select id="role" name="role" class="form-select">
                        <option value="student" <?= ($_POST['role'] ?? 'student') === 'student' ? 'selected' : '' ?>>طالب</option>
                        <option value="teacher" <?= ($_POST['role'] ?? '') === 'teacher' ? 'selected' : '' ?>>معلم</option>
                    </select>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password">كلمة المرور *</label>
                        <input type="password" id="password" name="password" class="form-input" 
                               placeholder="أدخل كلمة مرور قوية" required>
                        <div id="passwordStrength" class="password-strength"></div>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password">تأكيد كلمة المرور *</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-input" 
                               placeholder="أعد إدخال كلمة المرور" required>
                    </div>
                </div>
                
                <div class="checkbox-group">
                    <input type="checkbox" id="terms" name="terms" required>
                    <label for="terms">
                        أوافق على <a href="terms.php" target="_blank">الشروط والأحكام</a> 
                        و <a href="privacy.php" target="_blank">سياسة الخصوصية</a>
                    </label>
                </div>
                
                <button type="submit" class="btn-auth">
                    <i class="fas fa-user-plus"></i> إنشاء الحساب
                </button>
            </form>
            
            <div class="auth-links">
                <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm_password');
            const passwordStrength = document.getElementById('passwordStrength');
            
            // فحص قوة كلمة المرور
            password.addEventListener('input', function() {
                const value = this.value;
                let strength = 0;
                let message = '';
                
                if (value.length >= 6) strength++;
                if (value.match(/[a-z]/)) strength++;
                if (value.match(/[A-Z]/)) strength++;
                if (value.match(/[0-9]/)) strength++;
                if (value.match(/[^a-zA-Z0-9]/)) strength++;
                
                switch (strength) {
                    case 0:
                    case 1:
                        message = '<span class="strength-weak">ضعيفة</span>';
                        break;
                    case 2:
                    case 3:
                        message = '<span class="strength-medium">متوسطة</span>';
                        break;
                    case 4:
                    case 5:
                        message = '<span class="strength-strong">قوية</span>';
                        break;
                }
                
                passwordStrength.innerHTML = value ? `قوة كلمة المرور: ${message}` : '';
            });
            
            // التحقق من تطابق كلمة المرور
            confirmPassword.addEventListener('input', function() {
                if (this.value && this.value !== password.value) {
                    this.classList.add('error');
                } else {
                    this.classList.remove('error');
                }
            });
            
            // التحقق من صحة النموذج
            form.addEventListener('submit', function(e) {
                let isValid = true;
                const inputs = form.querySelectorAll('input[required], select[required]');
                
                inputs.forEach(input => {
                    if (!input.value.trim()) {
                        input.classList.add('error');
                        isValid = false;
                    } else {
                        input.classList.remove('error');
                    }
                });
                
                if (password.value !== confirmPassword.value) {
                    confirmPassword.classList.add('error');
                    isValid = false;
                    showMessage('كلمة المرور وتأكيدها غير متطابقتين', 'error');
                }
                
                if (!document.getElementById('terms').checked) {
                    isValid = false;
                    showMessage('يجب الموافقة على الشروط والأحكام', 'error');
                }
                
                if (!isValid) {
                    e.preventDefault();
                }
            });
        });
        
        function showMessage(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.innerHTML = `<i class="fas fa-${type === 'error' ? 'exclamation-circle' : 'check-circle'}"></i> ${message}`;
            
            const form = document.querySelector('form');
            form.parentNode.insertBefore(alertDiv, form);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
