# 🏫 منصة دروس وامتحانات وحلول مدرسية

منصة تعليمية شاملة مطورة بـ PHP + MySQL + HTML + CSS + JavaScript لتوفير محتوى تعليمي مجاني ومنظم لجميع المستويات التعليمية.

## ✨ المميزات الرئيسية

### 🎯 للطلاب
- **تصفح المحتوى**: دروس، امتحانات، وحلول نموذجية منظمة حسب المستوى والمادة
- **بحث متقدم**: إمكانية البحث بالكلمات المفتاحية والفلترة حسب النوع والمستوى
- **تحميل الملفات**: تحميل مباشر للملفات بصيغ مختلفة (PDF, DOC, PPT, إلخ)
- **نظام التقييم**: تقييم الملفات والتعليق عليها
- **المفضلة**: حفظ الملفات المفضلة للوصول السريع

### 👨‍🏫 للمعلمين
- **رفع المحتوى**: إضافة دروس وامتحانات وحلول جديدة
- **إدارة الملفات**: تنظيم وتصنيف المحتوى التعليمي
- **تتبع الإحصائيات**: مراقبة عدد التحميلات والمشاهدات

### 👨‍💼 للمديرين
- **لوحة تحكم شاملة**: إدارة المستخدمين والمحتوى والإعدادات
- **إحصائيات مفصلة**: تقارير عن استخدام المنصة والأنشطة
- **إدارة المستويات والمواد**: تخصيص التصنيفات التعليمية

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Server**: XAMPP (Apache + MySQL + PHP)
- **Icons**: Font Awesome 6
- **Design**: Responsive Design, RTL Support

## 📋 متطلبات النظام

- **XAMPP** (أو WAMP/LAMP)
- **PHP** 7.4 أو أحدث
- **MySQL** 5.7 أو أحدث
- **Apache** مع mod_rewrite مفعل
- **مساحة تخزين** 500MB على الأقل

## 🚀 طريقة التثبيت

### 1. تحضير البيئة
```bash
# تحميل وتثبيت XAMPP
# تشغيل Apache و MySQL من لوحة تحكم XAMPP
```

### 2. إعداد المشروع
```bash
# نسخ ملفات المشروع إلى مجلد htdocs
cp -r DRERRASSA/ C:/xampp/htdocs/school-platform/

# أو استخدام Git
cd C:/xampp/htdocs/
git clone [repository-url] school-platform
```

### 3. إعداد قاعدة البيانات
1. افتح **phpMyAdmin** من `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم `school_platform`
3. استورد ملف `db/database.sql`

```sql
-- أو تشغيل الأوامر التالية في phpMyAdmin
SOURCE C:/xampp/htdocs/school-platform/db/database.sql;
```

### 4. تكوين الاتصال
تحقق من إعدادات قاعدة البيانات في `db/config.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'school_platform');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 5. إعداد الصلاحيات
```bash
# تأكد من صلاحيات الكتابة لمجلد uploads
chmod 755 uploads/
```

### 6. الوصول للموقع
- **الموقع الرئيسي**: `http://localhost/school-platform/`
- **لوحة التحكم**: `http://localhost/school-platform/dashboard/`

## 👤 بيانات الدخول الافتراضية

### حساب المدير
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password`

## 📁 هيكل المشروع

```
school-platform/
├── assets/                 # الملفات الثابتة
│   ├── css/                # ملفات التنسيق
│   ├── js/                 # ملفات JavaScript
│   └── images/             # الصور
├── db/                     # قاعدة البيانات
│   ├── config.php          # إعدادات الاتصال
│   └── database.sql        # هيكل قاعدة البيانات
├── dashboard/              # لوحة التحكم
│   ├── index.php           # الصفحة الرئيسية
│   ├── add_document.php    # إضافة ملف
│   └── ...                 # صفحات أخرى
├── ajax/                   # طلبات AJAX
├── uploads/                # الملفات المرفوعة
├── index.php               # الصفحة الرئيسية
├── login.php               # تسجيل الدخول
├── register.php            # التسجيل
├── search.php              # البحث
├── level.php               # صفحة المستوى
├── subject.php             # صفحة المادة
├── view_document.php       # عرض الوثيقة
└── download.php            # تحميل الملف
```

## 🎨 المستويات والمواد المتاحة

### المستويات التعليمية
- **الابتدائي**: الصفوف 1-5
- **المتوسط**: الصفوف 6-9
- **الثانوي**: الصفوف 10-12

### الشهادات الرسمية 🎓
- **شهادة التعليم المتوسط**: مواضيع وحلول امتحانات شهادة التعليم المتوسط (2015-2024)
- **شهادة البكالوريا**: مواضيع وحلول امتحانات البكالوريا لجميع الشعب (2010-2024)

### المواد الدراسية
- الرياضيات
- اللغة العربية
- العلوم/الفيزياء/الكيمياء
- التاريخ والجغرافيا
- اللغات الأجنبية
- التربية الإسلامية

## 🔧 التخصيص والتطوير

### إضافة مستوى تعليمي جديد
```sql
INSERT INTO levels (name, description) VALUES ('المستوى الجديد', 'وصف المستوى');
```

### إضافة مادة دراسية جديدة
```sql
INSERT INTO subjects (level_id, name, description, icon, color) 
VALUES (1, 'المادة الجديدة', 'وصف المادة', 'fas fa-book', '#3498db');
```

### تخصيص التصميم
- عدل ملف `assets/css/style.css` لتغيير الألوان والخطوط
- استخدم متغيرات CSS للتخصيص السريع:
```css
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
}
```

## 🔒 الأمان

### الميزات الأمنية المطبقة
- **تشفير كلمات المرور**: باستخدام `password_hash()`
- **حماية من SQL Injection**: استخدام Prepared Statements
- **تنظيف البيانات**: دالة `sanitize()` لتنظيف المدخلات
- **التحقق من نوع الملفات**: قائمة بيضاء للأنواع المسموحة
- **حدود حجم الملفات**: حد أقصى 10MB
- **جلسات آمنة**: إدارة محكمة للجلسات

### توصيات إضافية للإنتاج
```php
// في ملف config.php
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
```

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **users**: المستخدمين وصلاحياتهم
- **levels**: المستويات التعليمية
- **subjects**: المواد الدراسية
- **documents**: الملفات والوثائق
- **ratings**: تقييمات الملفات
- **comments**: التعليقات
- **favorites**: المفضلة
- **activity_logs**: سجل الأنشطة

## 🚀 نشر الموقع

### للنشر على خادم مشترك
1. رفع الملفات عبر FTP
2. إنشاء قاعدة البيانات واستيراد البيانات
3. تحديث إعدادات `db/config.php`
4. تعيين صلاحيات مجلد `uploads/`

### للنشر على VPS
```bash
# تثبيت LAMP Stack
sudo apt update
sudo apt install apache2 mysql-server php php-mysql

# رفع الملفات
sudo cp -r school-platform/ /var/www/html/

# تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/html/school-platform/
sudo chmod -R 755 /var/www/html/school-platform/
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات**
```
تحقق من:
- تشغيل MySQL في XAMPP
- صحة بيانات الاتصال في config.php
- وجود قاعدة البيانات school_platform
```

**خطأ في رفع الملفات**
```
تحقق من:
- صلاحيات مجلد uploads/
- حجم الملف (أقل من 10MB)
- نوع الملف مدعوم
```

**مشاكل في التصميم**
```
تحقق من:
- تحميل ملفات CSS و JS
- اتصال الإنترنت لـ Font Awesome
- دعم المتصفح للـ CSS Grid
```

## 📞 الدعم والمساهمة

### الإبلاغ عن مشاكل
- أنشئ Issue جديد مع وصف مفصل للمشكلة
- أرفق لقطات شاشة إن أمكن
- اذكر نسخة PHP و MySQL المستخدمة

### المساهمة في التطوير
1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التعليقات
4. اختبر التغييرات
5. أرسل Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **Font Awesome** للأيقونات الرائعة
- **مجتمع PHP** للدعم والموارد
- **المطورين المساهمين** في تحسين المشروع

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة التعليم العربي**
