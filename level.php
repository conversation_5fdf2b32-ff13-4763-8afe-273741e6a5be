<?php
session_start();
require_once 'db/config.php';

// التحقق من وجود معرف المستوى
$levelId = $_GET['id'] ?? null;
if (!$levelId || !is_numeric($levelId)) {
    redirect('index.php');
}

// جلب معلومات المستوى
$stmt = $pdo->prepare("SELECT * FROM levels WHERE id = ?");
$stmt->execute([$levelId]);
$level = $stmt->fetch();

if (!$level) {
    redirect('index.php');
}

// جلب المواد الدراسية للمستوى
$subjectsStmt = $pdo->prepare("SELECT * FROM subjects WHERE level_id = ? ORDER BY name");
$subjectsStmt->execute([$levelId]);
$subjects = $subjectsStmt->fetchAll();

// جلب إحصائيات المستوى
$statsStmt = $pdo->prepare("
    SELECT 
        s.id as subject_id,
        s.name as subject_name,
        COUNT(d.id) as total_documents,
        SUM(CASE WHEN d.type = 'درس' THEN 1 ELSE 0 END) as lessons_count,
        SUM(CASE WHEN d.type = 'امتحان' THEN 1 ELSE 0 END) as exams_count,
        SUM(CASE WHEN d.type = 'حل' THEN 1 ELSE 0 END) as solutions_count
    FROM subjects s
    LEFT JOIN documents d ON s.id = d.subject_id
    WHERE s.level_id = ?
    GROUP BY s.id, s.name
    ORDER BY s.name
");
$statsStmt->execute([$levelId]);
$subjectStats = $statsStmt->fetchAll();

// جلب أحدث الوثائق للمستوى
$recentDocsStmt = $pdo->prepare("
    SELECT d.*, s.name as subject_name, s.color as subject_color
    FROM documents d
    JOIN subjects s ON d.subject_id = s.id
    WHERE s.level_id = ?
    ORDER BY d.upload_date DESC
    LIMIT 8
");
$recentDocsStmt->execute([$levelId]);
$recentDocuments = $recentDocsStmt->fetchAll();

// جلب جميع المستويات للقائمة الجانبية
$allLevels = getLevels();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $level['name'] ?> - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .level-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .level-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .level-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .breadcrumb {
            background: #f8f9fa;
            padding: 1rem 0;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .breadcrumb-list {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }
        
        .breadcrumb-list li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .breadcrumb-list a {
            color: #3498db;
            text-decoration: none;
        }
        
        .breadcrumb-list a:hover {
            text-decoration: underline;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 2rem;
            padding: 2rem 0;
        }
        
        .content-area {
            min-height: 500px;
        }
        
        .sidebar {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .sidebar h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }
        
        .sidebar ul {
            list-style: none;
            padding: 0;
        }
        
        .sidebar li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar a {
            display: block;
            padding: 0.7rem 1rem;
            color: #666;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar a:hover,
        .sidebar a.active {
            background: #3498db;
            color: white;
            transform: translateX(-5px);
        }
        
        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .subject-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .subject-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--subject-color, #3498db);
        }
        
        .subject-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .subject-icon {
            font-size: 3rem;
            color: var(--subject-color, #3498db);
            margin-bottom: 1rem;
        }
        
        .subject-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }
        
        .subject-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--subject-color, #3498db);
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }
        
        .recent-section {
            margin-top: 3rem;
        }
        
        .recent-section h2 {
            color: #2c3e50;
            margin-bottom: 2rem;
            text-align: center;
            font-size: 2rem;
        }
        
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }
        
        .document-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .document-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        .document-type {
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
            background: var(--subject-color, #3498db);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
        }
        
        .document-card h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .document-meta {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        
        .document-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            border-radius: 20px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: -1;
                position: static;
            }
            
            .level-header h1 {
                font-size: 2rem;
            }
            
            .subjects-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li class="dropdown">
                        <a href="#" class="active"><i class="fas fa-layer-group"></i> المستويات <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <?php foreach($allLevels as $lvl): ?>
                                <li><a href="level.php?id=<?= $lvl['id'] ?>" <?= $lvl['id'] == $levelId ? 'class="active"' : '' ?>><?= $lvl['name'] ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                    <li><a href="search.php"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isLoggedIn()): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if(isAdmin()): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Level Header -->
    <section class="level-header">
        <div class="container">
            <h1><i class="fas fa-layer-group"></i> <?= $level['name'] ?></h1>
            <p><?= $level['description'] ?? 'جميع المواد الدراسية والمحتوى التعليمي' ?></p>
        </div>
    </section>

    <!-- Breadcrumb -->
    <section class="breadcrumb">
        <div class="container">
            <ul class="breadcrumb-list">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><i class="fas fa-chevron-left"></i></li>
                <li><?= $level['name'] ?></li>
            </ul>
        </div>
    </section>

    <!-- Main Content -->
    <section class="main-content">
        <div class="container">
            <div class="content-area">
                <!-- Subjects Grid -->
                <div class="subjects-grid">
                    <?php foreach($subjectStats as $subject): ?>
                        <div class="subject-card" style="--subject-color: <?= $subjects[array_search($subject['subject_id'], array_column($subjects, 'id'))]['color'] ?? '#3498db' ?>">
                            <div class="subject-icon">
                                <i class="<?= $subjects[array_search($subject['subject_id'], array_column($subjects, 'id'))]['icon'] ?? 'fas fa-book' ?>"></i>
                            </div>
                            <h3><?= $subject['subject_name'] ?></h3>
                            
                            <div class="subject-stats">
                                <div class="stat-item">
                                    <div class="stat-number"><?= $subject['lessons_count'] ?></div>
                                    <div class="stat-label">درس</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?= $subject['exams_count'] ?></div>
                                    <div class="stat-label">امتحان</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?= $subject['solutions_count'] ?></div>
                                    <div class="stat-label">حل</div>
                                </div>
                            </div>
                            
                            <a href="subject.php?id=<?= $subject['subject_id'] ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left"></i> استكشف المادة
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Recent Documents -->
                <?php if($recentDocuments): ?>
                <div class="recent-section">
                    <h2><i class="fas fa-clock"></i> أحدث المحتويات</h2>
                    <div class="documents-grid">
                        <?php foreach($recentDocuments as $doc): ?>
                            <div class="document-card" style="--subject-color: <?= $doc['subject_color'] ?>">
                                <div class="document-type">
                                    <i class="fas fa-<?= $doc['type'] == 'درس' ? 'book' : ($doc['type'] == 'امتحان' ? 'file-alt' : 'check-circle') ?>"></i>
                                    <?= $doc['type'] ?>
                                </div>
                                <h4><?= $doc['title'] ?></h4>
                                <div class="document-meta">
                                    <i class="fas fa-book"></i> <?= $doc['subject_name'] ?> •
                                    <i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($doc['upload_date'])) ?>
                                </div>
                                <div class="document-actions">
                                    <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline btn-sm">عرض</a>
                                    <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-primary btn-sm">تحميل</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
                <h3><i class="fas fa-layer-group"></i> جميع المستويات</h3>
                <ul>
                    <?php foreach($allLevels as $lvl): ?>
                        <li>
                            <a href="level.php?id=<?= $lvl['id'] ?>" <?= $lvl['id'] == $levelId ? 'class="active"' : '' ?>>
                                <i class="fas fa-chevron-left"></i> <?= $lvl['name'] ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>

                <h3 style="margin-top: 2rem;"><i class="fas fa-search"></i> بحث سريع</h3>
                <form action="search.php" method="GET">
                    <input type="hidden" name="level" value="<?= $levelId ?>">
                    <div class="search-input-group" style="border-radius: 10px;">
                        <input type="text" name="q" placeholder="ابحث في <?= $level['name'] ?>..." style="border-radius: 10px;">
                        <button type="submit" style="border-radius: 10px;"><i class="fas fa-search"></i></button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>المستويات</h4>
                    <ul>
                        <?php foreach($allLevels as $lvl): ?>
                            <li><a href="level.php?id=<?= $lvl['id'] ?>"><?= $lvl['name'] ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
</body>
</html>
