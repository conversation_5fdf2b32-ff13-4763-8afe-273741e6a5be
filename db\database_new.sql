-- قاعدة بيانات منصة التعليم - الإصدار المحدث
-- تاريخ الإنشاء: 2024-12-19
-- الإصدار: 3.0 - مع دعم الشهادات الرسمية
-- المزايا الجديدة: شهادة التعليم المتوسط، شهادة البكالوريا، فلترة متقدمة

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS school_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE school_platform;

-- جدول المستويات التعليمية
CREATE TABLE levels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_name (name)
);

-- جدول المواد الدراسية
CREATE TABLE subjects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    level_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'fas fa-book',
    color VARCHAR(7) DEFAULT '#3498db',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    INDEX idx_level_id (level_id),
    INDEX idx_name (name)
);

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'teacher', 'student') DEFAULT 'student',
    avatar VARCHAR(255) DEFAULT NULL,
    bio TEXT DEFAULT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    address TEXT DEFAULT NULL,
    birth_date DATE DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(100) DEFAULT NULL,
    password_reset_token VARCHAR(100) DEFAULT NULL,
    password_reset_expires TIMESTAMP NULL DEFAULT NULL,
    last_login TIMESTAMP NULL DEFAULT NULL,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_active (is_active)
);

-- جدول الوثائق (محدث مع دعم الشهادات)
CREATE TABLE documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT DEFAULT 0,
    file_type VARCHAR(50) DEFAULT NULL,
    type ENUM('درس', 'امتحان', 'حل', 'تمرين', 'ملخص', 'موضوع شهادة', 'حل شهادة') NOT NULL,
    subject_id INT NOT NULL,
    uploaded_by INT NOT NULL,
    download_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    tags TEXT DEFAULT NULL,
    academic_year VARCHAR(20) DEFAULT NULL,
    exam_year VARCHAR(10) DEFAULT NULL,
    exam_session ENUM('الدورة العادية', 'دورة الاستدراك', 'دورة استثنائية') DEFAULT NULL,
    branch VARCHAR(100) DEFAULT NULL,
    semester ENUM('الأول', 'الثاني', 'الثالث') DEFAULT NULL,
    difficulty_level ENUM('سهل', 'متوسط', 'صعب') DEFAULT 'متوسط',
    language VARCHAR(10) DEFAULT 'ar',
    thumbnail VARCHAR(500) DEFAULT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_subject_id (subject_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_type (type),
    INDEX idx_exam_year (exam_year),
    INDEX idx_exam_session (exam_session),
    INDEX idx_upload_date (upload_date),
    INDEX idx_featured (is_featured),
    INDEX idx_approved (is_approved),
    FULLTEXT idx_search (title, description, tags)
);

-- جدول التقييمات
CREATE TABLE ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    rating TINYINT CHECK (rating >= 1 AND rating <= 5),
    review TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_document (user_id, document_id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating)
);

-- جدول التعليقات
CREATE TABLE comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    parent_id INT DEFAULT NULL,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_approved (is_approved)
);

-- جدول المفضلة
CREATE TABLE favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    document_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_document (user_id, document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_document_id (document_id)
);

-- جدول سجل التحميلات
CREATE TABLE download_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT DEFAULT NULL,
    download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_download_date (download_date),
    INDEX idx_ip_address (ip_address)
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_created_at (created_at)
);

-- جدول السنوات الدراسية للشهادات (جديد)
CREATE TABLE exam_years (
    id INT AUTO_INCREMENT PRIMARY KEY,
    year VARCHAR(10) NOT NULL,
    level_id INT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_year_level (year, level_id),
    INDEX idx_level_year (level_id, year),
    INDEX idx_active (is_active)
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- جدول سجل النشاطات
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) DEFAULT NULL,
    record_id INT DEFAULT NULL,
    old_values JSON DEFAULT NULL,
    new_values JSON DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
);

-- إدراج المستويات التعليمية (محدث)
INSERT INTO levels (name, description) VALUES
('الابتدائي', 'المرحلة الابتدائية من الصف الأول إلى الخامس'),
('المتوسط', 'المرحلة المتوسطة من الصف السادس إلى التاسع'),
('الثانوي', 'المرحلة الثانوية من الصف العاشر إلى الثاني عشر'),
('شهادة التعليم المتوسط', 'مواضيع وحلول امتحانات شهادة التعليم المتوسط حسب السنوات'),
('شهادة البكالوريا', 'مواضيع وحلول امتحانات شهادة البكالوريا حسب السنوات والشعب');

-- إدراج المواد الدراسية للمستويات العادية
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
-- المرحلة الابتدائية
(1, 'الرياضيات', 'مادة الرياضيات للمرحلة الابتدائية', 'fas fa-calculator', '#e74c3c'),
(1, 'اللغة العربية', 'مادة اللغة العربية للمرحلة الابتدائية', 'fas fa-book-open', '#2ecc71'),
(1, 'العلوم', 'مادة العلوم للمرحلة الابتدائية', 'fas fa-flask', '#9b59b6'),
(1, 'التربية الإسلامية', 'مادة التربية الإسلامية للمرحلة الابتدائية', 'fas fa-mosque', '#27ae60'),
(1, 'التاريخ', 'مادة التاريخ للمرحلة الابتدائية', 'fas fa-landmark', '#f39c12'),
(1, 'الجغرافيا', 'مادة الجغرافيا للمرحلة الابتدائية', 'fas fa-globe', '#3498db'),

-- المرحلة المتوسطة
(2, 'الرياضيات', 'مادة الرياضيات للمرحلة المتوسطة', 'fas fa-calculator', '#e74c3c'),
(2, 'اللغة العربية', 'مادة اللغة العربية للمرحلة المتوسطة', 'fas fa-book-open', '#2ecc71'),
(2, 'العلوم الفيزيائية', 'مادة العلوم الفيزيائية للمرحلة المتوسطة', 'fas fa-atom', '#9b59b6'),
(2, 'علوم الطبيعة والحياة', 'مادة علوم الطبيعة والحياة للمرحلة المتوسطة', 'fas fa-leaf', '#27ae60'),
(2, 'التاريخ والجغرافيا', 'مادة التاريخ والجغرافيا للمرحلة المتوسطة', 'fas fa-map', '#f39c12'),
(2, 'التربية الإسلامية', 'مادة التربية الإسلامية للمرحلة المتوسطة', 'fas fa-mosque', '#16a085'),
(2, 'اللغة الفرنسية', 'مادة اللغة الفرنسية للمرحلة المتوسطة', 'fas fa-language', '#3498db'),
(2, 'اللغة الإنجليزية', 'مادة اللغة الإنجليزية للمرحلة المتوسطة', 'fas fa-language', '#e67e22'),

-- المرحلة الثانوية
(3, 'الرياضيات', 'مادة الرياضيات للمرحلة الثانوية', 'fas fa-calculator', '#e74c3c'),
(3, 'الفيزياء', 'مادة الفيزياء للمرحلة الثانوية', 'fas fa-atom', '#9b59b6'),
(3, 'الكيمياء', 'مادة الكيمياء للمرحلة الثانوية', 'fas fa-flask', '#e67e22'),
(3, 'علوم الطبيعة والحياة', 'مادة علوم الطبيعة والحياة للمرحلة الثانوية', 'fas fa-dna', '#27ae60'),
(3, 'اللغة العربية وآدابها', 'مادة اللغة العربية وآدابها للمرحلة الثانوية', 'fas fa-book-open', '#2ecc71'),
(3, 'التاريخ والجغرافيا', 'مادة التاريخ والجغرافيا للمرحلة الثانوية', 'fas fa-globe-americas', '#f39c12'),
(3, 'الفلسفة', 'مادة الفلسفة للمرحلة الثانوية', 'fas fa-brain', '#34495e'),
(3, 'اللغة الفرنسية', 'مادة اللغة الفرنسية للمرحلة الثانوية', 'fas fa-language', '#3498db'),
(3, 'اللغة الإنجليزية', 'مادة اللغة الإنجليزية للمرحلة الثانوية', 'fas fa-language', '#e67e22'),
(3, 'التربية الإسلامية', 'مادة التربية الإسلامية للمرحلة الثانوية', 'fas fa-mosque', '#27ae60');

-- إدراج المواد الدراسية لشهادة التعليم المتوسط
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
(4, 'اللغة العربية', 'مواضيع وحلول اللغة العربية لشهادة التعليم المتوسط', 'fas fa-book-open', '#2ecc71'),
(4, 'الرياضيات', 'مواضيع وحلول الرياضيات لشهادة التعليم المتوسط', 'fas fa-calculator', '#e74c3c'),
(4, 'العلوم الفيزيائية', 'مواضيع وحلول العلوم الفيزيائية لشهادة التعليم المتوسط', 'fas fa-atom', '#9b59b6'),
(4, 'علوم الطبيعة والحياة', 'مواضيع وحلول علوم الطبيعة والحياة لشهادة التعليم المتوسط', 'fas fa-leaf', '#27ae60'),
(4, 'التاريخ والجغرافيا', 'مواضيع وحلول التاريخ والجغرافيا لشهادة التعليم المتوسط', 'fas fa-map', '#f39c12'),
(4, 'التربية الإسلامية', 'مواضيع وحلول التربية الإسلامية لشهادة التعليم المتوسط', 'fas fa-mosque', '#16a085'),
(4, 'اللغة الفرنسية', 'مواضيع وحلول اللغة الفرنسية لشهادة التعليم المتوسط', 'fas fa-language', '#3498db'),
(4, 'اللغة الإنجليزية', 'مواضيع وحلول اللغة الإنجليزية لشهادة التعليم المتوسط', 'fas fa-language', '#e67e22');

-- إدراج المواد الدراسية لشهادة البكالوريا
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
-- شعبة العلوم التجريبية
(5, 'الرياضيات - علوم تجريبية', 'مواضيع وحلول الرياضيات لشعبة العلوم التجريبية', 'fas fa-calculator', '#e74c3c'),
(5, 'الفيزياء - علوم تجريبية', 'مواضيع وحلول الفيزياء لشعبة العلوم التجريبية', 'fas fa-atom', '#9b59b6'),
(5, 'علوم الطبيعة والحياة - علوم تجريبية', 'مواضيع وحلول علوم الطبيعة والحياة لشعبة العلوم التجريبية', 'fas fa-dna', '#27ae60'),
-- شعبة الرياضيات
(5, 'الرياضيات - رياضيات', 'مواضيع وحلول الرياضيات لشعبة الرياضيات', 'fas fa-calculator', '#c0392b'),
(5, 'الفيزياء - رياضيات', 'مواضيع وحلول الفيزياء لشعبة الرياضيات', 'fas fa-atom', '#8e44ad'),
-- شعبة تقني رياضي
(5, 'الرياضيات - تقني رياضي', 'مواضيع وحلول الرياضيات لشعبة تقني رياضي', 'fas fa-calculator', '#d35400'),
(5, 'الفيزياء - تقني رياضي', 'مواضيع وحلول الفيزياء لشعبة تقني رياضي', 'fas fa-atom', '#7f8c8d'),
(5, 'الهندسة المدنية', 'مواضيع وحلول الهندسة المدنية لشعبة تقني رياضي', 'fas fa-building', '#34495e'),
(5, 'الهندسة الميكانيكية', 'مواضيع وحلول الهندسة الميكانيكية لشعبة تقني رياضي', 'fas fa-cogs', '#95a5a6'),
(5, 'الهندسة الكهربائية', 'مواضيع وحلول الهندسة الكهربائية لشعبة تقني رياضي', 'fas fa-bolt', '#f1c40f'),
-- شعبة الآداب والفلسفة
(5, 'الفلسفة - آداب وفلسفة', 'مواضيع وحلول الفلسفة لشعبة الآداب والفلسفة', 'fas fa-brain', '#34495e'),
(5, 'اللغة العربية - آداب وفلسفة', 'مواضيع وحلول اللغة العربية لشعبة الآداب والفلسفة', 'fas fa-book-open', '#2ecc71'),
(5, 'التاريخ والجغرافيا - آداب وفلسفة', 'مواضيع وحلول التاريخ والجغرافيا لشعبة الآداب والفلسفة', 'fas fa-globe-americas', '#f39c12'),
-- شعبة اللغات الأجنبية
(5, 'اللغة الإنجليزية - لغات أجنبية', 'مواضيع وحلول اللغة الإنجليزية لشعبة اللغات الأجنبية', 'fas fa-language', '#e67e22'),
(5, 'اللغة الفرنسية - لغات أجنبية', 'مواضيع وحلول اللغة الفرنسية لشعبة اللغات الأجنبية', 'fas fa-language', '#3498db'),
(5, 'اللغة الألمانية - لغات أجنبية', 'مواضيع وحلول اللغة الألمانية لشعبة اللغات الأجنبية', 'fas fa-language', '#e74c3c'),
(5, 'اللغة الإسبانية - لغات أجنبية', 'مواضيع وحلول اللغة الإسبانية لشعبة اللغات الأجنبية', 'fas fa-language', '#f39c12'),
-- مواد مشتركة للبكالوريا
(5, 'اللغة العربية - مشترك', 'مواضيع وحلول اللغة العربية المشتركة لجميع الشعب', 'fas fa-book-open', '#2ecc71'),
(5, 'التربية الإسلامية - مشترك', 'مواضيع وحلول التربية الإسلامية المشتركة لجميع الشعب', 'fas fa-mosque', '#16a085'),
(5, 'اللغة الفرنسية - مشترك', 'مواضيع وحلول اللغة الفرنسية المشتركة لجميع الشعب', 'fas fa-language', '#3498db'),
(5, 'اللغة الإنجليزية - مشترك', 'مواضيع وحلول اللغة الإنجليزية المشتركة لجميع الشعب', 'fas fa-language', '#e67e22');

-- إدراج السنوات الدراسية لشهادة التعليم المتوسط
INSERT INTO exam_years (year, level_id, description) VALUES
('2024', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2024'),
('2023', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2023'),
('2022', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2022'),
('2021', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2021'),
('2020', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2020'),
('2019', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2019'),
('2018', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2018'),
('2017', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2017'),
('2016', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2016'),
('2015', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2015');

-- إدراج السنوات الدراسية لشهادة البكالوريا
INSERT INTO exam_years (year, level_id, description) VALUES
('2024', 5, 'مواضيع وحلول شهادة البكالوريا 2024'),
('2023', 5, 'مواضيع وحلول شهادة البكالوريا 2023'),
('2022', 5, 'مواضيع وحلول شهادة البكالوريا 2022'),
('2021', 5, 'مواضيع وحلول شهادة البكالوريا 2021'),
('2020', 5, 'مواضيع وحلول شهادة البكالوريا 2020'),
('2019', 5, 'مواضيع وحلول شهادة البكالوريا 2019'),
('2018', 5, 'مواضيع وحلول شهادة البكالوريا 2018'),
('2017', 5, 'مواضيع وحلول شهادة البكالوريا 2017'),
('2016', 5, 'مواضيع وحلول شهادة البكالوريا 2016'),
('2015', 5, 'مواضيع وحلول شهادة البكالوريا 2015'),
('2014', 5, 'مواضيع وحلول شهادة البكالوريا 2014'),
('2013', 5, 'مواضيع وحلول شهادة البكالوريا 2013'),
('2012', 5, 'مواضيع وحلول شهادة البكالوريا 2012'),
('2011', 5, 'مواضيع وحلول شهادة البكالوريا 2011'),
('2010', 5, 'مواضيع وحلول شهادة البكالوريا 2010');

-- إنشاء مستخدم مدير افتراضي
INSERT INTO users (username, email, password, full_name, role, is_active, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE, TRUE);

-- إدراج بيانات تجريبية للوثائق - شهادة التعليم المتوسط
INSERT INTO documents (title, description, file_path, type, subject_id, uploaded_by, exam_year, exam_session, academic_year, tags) VALUES
-- مواضيع شهادة التعليم المتوسط 2024
('موضوع الرياضيات - شهادة التعليم المتوسط 2024', 'موضوع امتحان الرياضيات للدورة العادية لشهادة التعليم المتوسط 2024', 'uploads/bem/2024/math_subject_2024.pdf', 'موضوع شهادة', 22, 1, '2024', 'الدورة العادية', '2023-2024', 'رياضيات,شهادة,متوسط,2024'),
('حل موضوع الرياضيات - شهادة التعليم المتوسط 2024', 'الحل النموذجي لموضوع امتحان الرياضيات للدورة العادية لشهادة التعليم المتوسط 2024', 'uploads/bem/2024/math_solution_2024.pdf', 'حل شهادة', 22, 1, '2024', 'الدورة العادية', '2023-2024', 'رياضيات,حل,شهادة,متوسط,2024'),
('موضوع اللغة العربية - شهادة التعليم المتوسط 2024', 'موضوع امتحان اللغة العربية للدورة العادية لشهادة التعليم المتوسط 2024', 'uploads/bem/2024/arabic_subject_2024.pdf', 'موضوع شهادة', 21, 1, '2024', 'الدورة العادية', '2023-2024', 'عربية,شهادة,متوسط,2024'),
('حل موضوع اللغة العربية - شهادة التعليم المتوسط 2024', 'الحل النموذجي لموضوع امتحان اللغة العربية للدورة العادية لشهادة التعليم المتوسط 2024', 'uploads/bem/2024/arabic_solution_2024.pdf', 'حل شهادة', 21, 1, '2024', 'الدورة العادية', '2023-2024', 'عربية,حل,شهادة,متوسط,2024'),
('موضوع العلوم الفيزيائية - شهادة التعليم المتوسط 2024', 'موضوع امتحان العلوم الفيزيائية للدورة العادية لشهادة التعليم المتوسط 2024', 'uploads/bem/2024/physics_subject_2024.pdf', 'موضوع شهادة', 23, 1, '2024', 'الدورة العادية', '2023-2024', 'فيزياء,شهادة,متوسط,2024'),
('حل موضوع العلوم الفيزيائية - شهادة التعليم المتوسط 2024', 'الحل النموذجي لموضوع امتحان العلوم الفيزيائية للدورة العادية لشهادة التعليم المتوسط 2024', 'uploads/bem/2024/physics_solution_2024.pdf', 'حل شهادة', 23, 1, '2024', 'الدورة العادية', '2023-2024', 'فيزياء,حل,شهادة,متوسط,2024'),

-- مواضيع شهادة التعليم المتوسط 2023
('موضوع الرياضيات - شهادة التعليم المتوسط 2023', 'موضوع امتحان الرياضيات للدورة العادية لشهادة التعليم المتوسط 2023', 'uploads/bem/2023/math_subject_2023.pdf', 'موضوع شهادة', 22, 1, '2023', 'الدورة العادية', '2022-2023', 'رياضيات,شهادة,متوسط,2023'),
('حل موضوع الرياضيات - شهادة التعليم المتوسط 2023', 'الحل النموذجي لموضوع امتحان الرياضيات للدورة العادية لشهادة التعليم المتوسط 2023', 'uploads/bem/2023/math_solution_2023.pdf', 'حل شهادة', 22, 1, '2023', 'الدورة العادية', '2022-2023', 'رياضيات,حل,شهادة,متوسط,2023'),
('موضوع اللغة العربية - شهادة التعليم المتوسط 2023', 'موضوع امتحان اللغة العربية للدورة العادية لشهادة التعليم المتوسط 2023', 'uploads/bem/2023/arabic_subject_2023.pdf', 'موضوع شهادة', 21, 1, '2023', 'الدورة العادية', '2022-2023', 'عربية,شهادة,متوسط,2023'),
('حل موضوع اللغة العربية - شهادة التعليم المتوسط 2023', 'الحل النموذجي لموضوع امتحان اللغة العربية للدورة العادية لشهادة التعليم المتوسط 2023', 'uploads/bem/2023/arabic_solution_2023.pdf', 'حل شهادة', 21, 1, '2023', 'الدورة العادية', '2022-2023', 'عربية,حل,شهادة,متوسط,2023');

-- إدراج بيانات تجريبية للوثائق - شهادة البكالوريا
INSERT INTO documents (title, description, file_path, type, subject_id, uploaded_by, exam_year, exam_session, branch, academic_year, tags) VALUES
-- مواضيع شهادة البكالوريا 2024 - شعبة العلوم التجريبية
('موضوع الرياضيات - بكالوريا 2024 علوم تجريبية', 'موضوع امتحان الرياضيات للدورة العادية لشهادة البكالوريا 2024 شعبة العلوم التجريبية', 'uploads/bac/2024/math_se_subject_2024.pdf', 'موضوع شهادة', 29, 1, '2024', 'الدورة العادية', 'علوم تجريبية', '2023-2024', 'رياضيات,بكالوريا,علوم تجريبية,2024'),
('حل موضوع الرياضيات - بكالوريا 2024 علوم تجريبية', 'الحل النموذجي لموضوع امتحان الرياضيات للدورة العادية لشهادة البكالوريا 2024 شعبة العلوم التجريبية', 'uploads/bac/2024/math_se_solution_2024.pdf', 'حل شهادة', 29, 1, '2024', 'الدورة العادية', 'علوم تجريبية', '2023-2024', 'رياضيات,حل,بكالوريا,علوم تجريبية,2024'),
('موضوع الفيزياء - بكالوريا 2024 علوم تجريبية', 'موضوع امتحان الفيزياء للدورة العادية لشهادة البكالوريا 2024 شعبة العلوم التجريبية', 'uploads/bac/2024/physics_se_subject_2024.pdf', 'موضوع شهادة', 30, 1, '2024', 'الدورة العادية', 'علوم تجريبية', '2023-2024', 'فيزياء,بكالوريا,علوم تجريبية,2024'),
('حل موضوع الفيزياء - بكالوريا 2024 علوم تجريبية', 'الحل النموذجي لموضوع امتحان الفيزياء للدورة العادية لشهادة البكالوريا 2024 شعبة العلوم التجريبية', 'uploads/bac/2024/physics_se_solution_2024.pdf', 'حل شهادة', 30, 1, '2024', 'الدورة العادية', 'علوم تجريبية', '2023-2024', 'فيزياء,حل,بكالوريا,علوم تجريبية,2024'),
('موضوع علوم الطبيعة والحياة - بكالوريا 2024 علوم تجريبية', 'موضوع امتحان علوم الطبيعة والحياة للدورة العادية لشهادة البكالوريا 2024 شعبة العلوم التجريبية', 'uploads/bac/2024/biology_se_subject_2024.pdf', 'موضوع شهادة', 31, 1, '2024', 'الدورة العادية', 'علوم تجريبية', '2023-2024', 'أحياء,بكالوريا,علوم تجريبية,2024'),
('حل موضوع علوم الطبيعة والحياة - بكالوريا 2024 علوم تجريبية', 'الحل النموذجي لموضوع امتحان علوم الطبيعة والحياة للدورة العادية لشهادة البكالوريا 2024 شعبة العلوم التجريبية', 'uploads/bac/2024/biology_se_solution_2024.pdf', 'حل شهادة', 31, 1, '2024', 'الدورة العادية', 'علوم تجريبية', '2023-2024', 'أحياء,حل,بكالوريا,علوم تجريبية,2024'),

-- مواضيع شهادة البكالوريا 2024 - شعبة الرياضيات
('موضوع الرياضيات - بكالوريا 2024 رياضيات', 'موضوع امتحان الرياضيات للدورة العادية لشهادة البكالوريا 2024 شعبة الرياضيات', 'uploads/bac/2024/math_m_subject_2024.pdf', 'موضوع شهادة', 32, 1, '2024', 'الدورة العادية', 'رياضيات', '2023-2024', 'رياضيات,بكالوريا,رياضيات,2024'),
('حل موضوع الرياضيات - بكالوريا 2024 رياضيات', 'الحل النموذجي لموضوع امتحان الرياضيات للدورة العادية لشهادة البكالوريا 2024 شعبة الرياضيات', 'uploads/bac/2024/math_m_solution_2024.pdf', 'حل شهادة', 32, 1, '2024', 'الدورة العادية', 'رياضيات', '2023-2024', 'رياضيات,حل,بكالوريا,رياضيات,2024'),
('موضوع الفيزياء - بكالوريا 2024 رياضيات', 'موضوع امتحان الفيزياء للدورة العادية لشهادة البكالوريا 2024 شعبة الرياضيات', 'uploads/bac/2024/physics_m_subject_2024.pdf', 'موضوع شهادة', 33, 1, '2024', 'الدورة العادية', 'رياضيات', '2023-2024', 'فيزياء,بكالوريا,رياضيات,2024'),
('حل موضوع الفيزياء - بكالوريا 2024 رياضيات', 'الحل النموذجي لموضوع امتحان الفيزياء للدورة العادية لشهادة البكالوريا 2024 شعبة الرياضيات', 'uploads/bac/2024/physics_m_solution_2024.pdf', 'حل شهادة', 33, 1, '2024', 'الدورة العادية', 'رياضيات', '2023-2024', 'فيزياء,حل,بكالوريا,رياضيات,2024'),

-- مواضيع مشتركة للبكالوريا 2024
('موضوع اللغة العربية - بكالوريا 2024 مشترك', 'موضوع امتحان اللغة العربية المشترك للدورة العادية لشهادة البكالوريا 2024', 'uploads/bac/2024/arabic_common_subject_2024.pdf', 'موضوع شهادة', 45, 1, '2024', 'الدورة العادية', 'مشترك', '2023-2024', 'عربية,بكالوريا,مشترك,2024'),
('حل موضوع اللغة العربية - بكالوريا 2024 مشترك', 'الحل النموذجي لموضوع امتحان اللغة العربية المشترك للدورة العادية لشهادة البكالوريا 2024', 'uploads/bac/2024/arabic_common_solution_2024.pdf', 'حل شهادة', 45, 1, '2024', 'الدورة العادية', 'مشترك', '2023-2024', 'عربية,حل,بكالوريا,مشترك,2024'),
('موضوع التربية الإسلامية - بكالوريا 2024 مشترك', 'موضوع امتحان التربية الإسلامية المشترك للدورة العادية لشهادة البكالوريا 2024', 'uploads/bac/2024/islamic_common_subject_2024.pdf', 'موضوع شهادة', 46, 1, '2024', 'الدورة العادية', 'مشترك', '2023-2024', 'إسلامية,بكالوريا,مشترك,2024'),
('حل موضوع التربية الإسلامية - بكالوريا 2024 مشترك', 'الحل النموذجي لموضوع امتحان التربية الإسلامية المشترك للدورة العادية لشهادة البكالوريا 2024', 'uploads/bac/2024/islamic_common_solution_2024.pdf', 'حل شهادة', 46, 1, '2024', 'الدورة العادية', 'مشترك', '2023-2024', 'إسلامية,حل,بكالوريا,مشترك,2024');

-- إدراج إعدادات النظام
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'منصة التعليم', 'string', 'اسم الموقع', TRUE),
('site_description', 'منصة تعليمية شاملة للمراحل الدراسية والشهادات الرسمية', 'string', 'وصف الموقع', TRUE),
('site_keywords', 'تعليم,مدرسة,شهادات,امتحانات,دروس', 'string', 'كلمات مفتاحية للموقع', TRUE),
('admin_email', '<EMAIL>', 'string', 'بريد المدير الإلكتروني', FALSE),
('max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف بالبايت (10MB)', FALSE),
('allowed_file_types', 'pdf,doc,docx,ppt,pptx,xls,xlsx,jpg,jpeg,png,gif', 'string', 'أنواع الملفات المسموحة', FALSE),
('enable_registration', 'true', 'boolean', 'تفعيل التسجيل الجديد', FALSE),
('enable_comments', 'true', 'boolean', 'تفعيل التعليقات', FALSE),
('enable_ratings', 'true', 'boolean', 'تفعيل التقييمات', FALSE),
('items_per_page', '12', 'number', 'عدد العناصر في الصفحة الواحدة', FALSE),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', FALSE),
('google_analytics_id', '', 'string', 'معرف Google Analytics', FALSE),
('facebook_url', '', 'string', 'رابط صفحة Facebook', TRUE),
('twitter_url', '', 'string', 'رابط صفحة Twitter', TRUE),
('youtube_url', '', 'string', 'رابط قناة YouTube', TRUE),
('contact_phone', '', 'string', 'رقم الهاتف للتواصل', TRUE),
('contact_email', '<EMAIL>', 'string', 'بريد التواصل', TRUE),
('contact_address', '', 'string', 'عنوان المؤسسة', TRUE);

-- تحديث عدادات التحميل والمشاهدة للوثائق التجريبية
UPDATE documents SET download_count = FLOOR(RAND() * 100) + 10, view_count = FLOOR(RAND() * 500) + 50;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_documents_exam_search ON documents(type, exam_year, exam_session);
CREATE INDEX idx_documents_level_type ON documents(subject_id, type);
CREATE INDEX idx_subjects_level_name ON subjects(level_id, name);
CREATE INDEX idx_users_role_active ON users(role, is_active);

-- إنشاء views لتسهيل الاستعلامات
CREATE VIEW exam_documents_view AS
SELECT
    d.id,
    d.title,
    d.description,
    d.file_path,
    d.type,
    d.exam_year,
    d.exam_session,
    d.branch,
    d.download_count,
    d.view_count,
    d.upload_date,
    s.name as subject_name,
    s.color as subject_color,
    s.icon as subject_icon,
    l.name as level_name,
    l.id as level_id,
    u.full_name as uploader_name
FROM documents d
JOIN subjects s ON d.subject_id = s.id
JOIN levels l ON s.level_id = l.id
JOIN users u ON d.uploaded_by = u.id
WHERE l.id IN (4, 5) AND d.type IN ('موضوع شهادة', 'حل شهادة')
ORDER BY d.exam_year DESC, d.upload_date DESC;

-- إنشاء view للإحصائيات
CREATE VIEW exam_statistics_view AS
SELECT
    l.id as level_id,
    l.name as level_name,
    COUNT(DISTINCT s.id) as total_subjects,
    COUNT(DISTINCT d.exam_year) as total_years,
    COUNT(CASE WHEN d.type = 'موضوع شهادة' THEN 1 END) as total_exam_subjects,
    COUNT(CASE WHEN d.type = 'حل شهادة' THEN 1 END) as total_solutions,
    COUNT(d.id) as total_documents,
    SUM(d.download_count) as total_downloads,
    SUM(d.view_count) as total_views
FROM levels l
LEFT JOIN subjects s ON l.id = s.level_id
LEFT JOIN documents d ON s.id = d.subject_id AND d.type IN ('موضوع شهادة', 'حل شهادة')
WHERE l.id IN (4, 5)
GROUP BY l.id, l.name;

-- إنشاء stored procedures مفيدة
DELIMITER //

CREATE PROCEDURE GetExamDocumentsByYear(IN exam_year VARCHAR(10), IN level_id INT)
BEGIN
    SELECT * FROM exam_documents_view
    WHERE (exam_year IS NULL OR exam_year = exam_year)
    AND (level_id IS NULL OR level_id = level_id)
    ORDER BY upload_date DESC;
END //

CREATE PROCEDURE GetExamStatistics(IN level_id INT)
BEGIN
    SELECT * FROM exam_statistics_view
    WHERE (level_id IS NULL OR level_id = level_id);
END //

CREATE PROCEDURE SearchExamDocuments(
    IN search_query VARCHAR(255),
    IN level_id INT,
    IN subject_id INT,
    IN exam_year VARCHAR(10),
    IN doc_type VARCHAR(50)
)
BEGIN
    SELECT * FROM exam_documents_view
    WHERE (search_query IS NULL OR title LIKE CONCAT('%', search_query, '%') OR description LIKE CONCAT('%', search_query, '%'))
    AND (level_id IS NULL OR level_id = level_id)
    AND (subject_id IS NULL OR subject_id = subject_id)
    AND (exam_year IS NULL OR exam_year = exam_year)
    AND (doc_type IS NULL OR type = doc_type)
    ORDER BY upload_date DESC;
END //

DELIMITER ;

-- إنشاء triggers لتحديث الإحصائيات تلقائياً
DELIMITER //

CREATE TRIGGER update_download_count
AFTER INSERT ON download_logs
FOR EACH ROW
BEGIN
    UPDATE documents
    SET download_count = download_count + 1
    WHERE id = NEW.document_id;
END //

CREATE TRIGGER update_view_count_on_rating
AFTER INSERT ON ratings
FOR EACH ROW
BEGIN
    UPDATE documents
    SET view_count = view_count + 1
    WHERE id = NEW.document_id;
END //

DELIMITER ;

-- إنشاء events لتنظيف البيانات القديمة (اختياري)
-- CREATE EVENT cleanup_old_logs
-- ON SCHEDULE EVERY 1 MONTH
-- DO
--   DELETE FROM download_logs WHERE download_date < DATE_SUB(NOW(), INTERVAL 6 MONTH);

-- إنهاء المعاملة
COMMIT;

-- رسالة نجاح
SELECT 'تم إنشاء قاعدة البيانات بنجاح مع دعم الشهادات الرسمية!' as message,
       COUNT(*) as total_documents,
       (SELECT COUNT(*) FROM subjects WHERE level_id IN (4,5)) as exam_subjects,
       (SELECT COUNT(*) FROM exam_years) as exam_years_count
FROM documents
WHERE type IN ('موضوع شهادة', 'حل شهادة');
