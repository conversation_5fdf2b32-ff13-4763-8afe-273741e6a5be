<?php
// ملف اختبار للتأكد من عمل PHP و Apache
echo "<h1>🎉 اختبار نجح!</h1>";
echo "<h2>معلومات الخادم:</h2>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>Server Software:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</li>";
echo "<li><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</li>";
echo "<li><strong>Script Name:</strong> " . $_SERVER['SCRIPT_NAME'] . "</li>";
echo "<li><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</li>";
echo "</ul>";

echo "<h2>اختبار قاعدة البيانات:</h2>";
try {
    require_once 'db/config.php';
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات نجح!</p>";
    
    // اختبار الجداول
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "<p><strong>الجداول الموجودة (" . count($tables) . "):</strong></p>";
    echo "<ul>";
    foreach($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
} catch(Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

echo "<h2>اختبار الملفات:</h2>";
$files_to_check = [
    'index.php',
    'db/config.php',
    'assets/css/style.css',
    'assets/js/script.js',
    'includes/header.php',
    'includes/footer.php'
];

echo "<ul>";
foreach($files_to_check as $file) {
    if(file_exists($file)) {
        echo "<li style='color: green;'>✅ $file</li>";
    } else {
        echo "<li style='color: red;'>❌ $file</li>";
    }
}
echo "</ul>";

echo "<h2>الروابط للاختبار:</h2>";
echo "<ul>";
echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
echo "<li><a href='fix_database.php'>إصلاح قاعدة البيانات</a></li>";
echo "<li><a href='level.php?id=1'>صفحة مستوى</a></li>";
echo "</ul>";

echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
h1, h2 { color: #2c3e50; }
ul { background: white; padding: 15px; border-radius: 5px; }
li { margin: 5px 0; }
a { color: #3498db; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";
?>
