-- إصلاح سريع: إضافة أعمدة الشهادات إلى جدول الوثائق
-- تاريخ الإنشاء: 2024-12-19
-- الغرض: إضافة الأعمدة المطلوبة لعمل قسم الشهادات

-- التحقق من وجود الأعمدة وإضافتها إذا لم تكن موجودة
SET @sql = '';

-- إضافة عمود exam_year
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'documents' 
AND COLUMN_NAME = 'exam_year';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE documents ADD COLUMN exam_year VARCHAR(10) DEFAULT NULL AFTER academic_year;', 
    'SELECT "Column exam_year already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضا<PERSON>ة عمود exam_session
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'documents' 
AND COLUMN_NAME = 'exam_session';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE documents ADD COLUMN exam_session ENUM(\'الدورة العادية\', \'دورة الاستدراك\', \'دورة استثنائية\') DEFAULT NULL AFTER exam_year;', 
    'SELECT "Column exam_session already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- إضافة عمود branch
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'documents' 
AND COLUMN_NAME = 'branch';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE documents ADD COLUMN branch VARCHAR(100) DEFAULT NULL AFTER exam_session;', 
    'SELECT "Column branch already exists" as message;');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- تحديث enum للأنواع لإضافة أنواع الشهادات
ALTER TABLE documents 
MODIFY COLUMN type ENUM('درس', 'امتحان', 'حل', 'تمرين', 'ملخص', 'موضوع شهادة', 'حل شهادة') NOT NULL;

-- إضافة بيانات تجريبية للشهادات (اختيارية)
-- تحديث بعض الوثائق الموجودة لتصبح مواضيع شهادات
UPDATE documents 
SET type = 'موضوع شهادة', exam_year = '2024', exam_session = 'الدورة العادية'
WHERE type = 'امتحان' AND subject_id IN (
    SELECT id FROM subjects WHERE level_id IN (4, 5)
) LIMIT 5;

UPDATE documents 
SET type = 'حل شهادة', exam_year = '2024', exam_session = 'الدورة العادية'
WHERE type = 'حل' AND subject_id IN (
    SELECT id FROM subjects WHERE level_id IN (4, 5)
) LIMIT 5;

-- إضافة بعض البيانات التجريبية للسنوات السابقة
UPDATE documents 
SET exam_year = '2023', exam_session = 'الدورة العادية'
WHERE type IN ('موضوع شهادة', 'حل شهادة') AND exam_year IS NULL
LIMIT 3;

UPDATE documents 
SET exam_year = '2022', exam_session = 'دورة الاستدراك'
WHERE type IN ('موضوع شهادة', 'حل شهادة') AND exam_year IS NULL
LIMIT 2;

-- التحقق من النتائج
SELECT 'تم إضافة الأعمدة بنجاح!' as message;

SELECT 
    'إحصائيات الشهادات:' as info,
    COUNT(*) as total_exam_docs,
    COUNT(CASE WHEN type = 'موضوع شهادة' THEN 1 END) as exam_subjects,
    COUNT(CASE WHEN type = 'حل شهادة' THEN 1 END) as exam_solutions,
    COUNT(DISTINCT exam_year) as available_years
FROM documents 
WHERE type IN ('موضوع شهادة', 'حل شهادة');

-- عرض عينة من الوثائق المحدثة
SELECT 
    id, title, type, exam_year, exam_session,
    (SELECT name FROM subjects WHERE id = subject_id) as subject_name
FROM documents 
WHERE type IN ('موضوع شهادة', 'حل شهادة')
ORDER BY exam_year DESC, type
LIMIT 10;
