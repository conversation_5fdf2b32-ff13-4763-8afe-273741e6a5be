<?php
session_start();
require_once '../db/config.php';

header('Content-Type: application/json');

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$documentId = $input['document_id'] ?? null;

if (!$documentId) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

try {
    // التحقق من وجود الوثيقة
    $docStmt = $pdo->prepare("SELECT id FROM documents WHERE id = ? AND is_approved = 1");
    $docStmt->execute([$documentId]);
    
    if (!$docStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الوثيقة غير موجودة']);
        exit;
    }
    
    // التحقق من وجود المفضلة
    $checkStmt = $pdo->prepare("SELECT id FROM favorites WHERE user_id = ? AND document_id = ?");
    $checkStmt->execute([$_SESSION['user_id'], $documentId]);
    $exists = $checkStmt->fetch();
    
    if ($exists) {
        // إزالة من المفضلة
        $deleteStmt = $pdo->prepare("DELETE FROM favorites WHERE user_id = ? AND document_id = ?");
        $deleteStmt->execute([$_SESSION['user_id'], $documentId]);
        
        logActivity($_SESSION['user_id'], 'remove_favorite', "إزالة الوثيقة $documentId من المفضلة");
        
        echo json_encode([
            'success' => true, 
            'is_favorite' => false,
            'message' => 'تم إزالة الملف من المفضلة'
        ]);
    } else {
        // إضافة للمفضلة
        $insertStmt = $pdo->prepare("INSERT INTO favorites (user_id, document_id, created_at) VALUES (?, ?, NOW())");
        $insertStmt->execute([$_SESSION['user_id'], $documentId]);
        
        logActivity($_SESSION['user_id'], 'add_favorite', "إضافة الوثيقة $documentId للمفضلة");
        
        echo json_encode([
            'success' => true, 
            'is_favorite' => true,
            'message' => 'تم إضافة الملف للمفضلة'
        ]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
