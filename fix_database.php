<?php
// ملف إصلاح قاعدة البيانات - إضافة أعمدة الشهادات
require_once 'db/config.php';

echo "<h2>إصلاح قاعدة البيانات - إضافة أعمدة الشهادات</h2>";

try {
    // التحقق من وجود الأعمدة
    $checkExamYear = $pdo->query("SHOW COLUMNS FROM documents LIKE 'exam_year'")->rowCount();
    $checkExamSession = $pdo->query("SHOW COLUMNS FROM documents LIKE 'exam_session'")->rowCount();
    $checkBranch = $pdo->query("SHOW COLUMNS FROM documents LIKE 'branch'")->rowCount();
    
    echo "<h3>حالة الأعمدة الحالية:</h3>";
    echo "<ul>";
    echo "<li>exam_year: " . ($checkExamYear ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>exam_session: " . ($checkExamSession ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "<li>branch: " . ($checkBranch ? "✅ موجود" : "❌ غير موجود") . "</li>";
    echo "</ul>";
    
    $needsUpdate = !$checkExamYear || !$checkExamSession || !$checkBranch;
    
    if ($needsUpdate) {
        echo "<h3>بدء عملية التحديث...</h3>";
        
        // إضافة عمود exam_year
        if (!$checkExamYear) {
            $pdo->exec("ALTER TABLE documents ADD COLUMN exam_year VARCHAR(10) DEFAULT NULL AFTER academic_year");
            echo "<p>✅ تم إضافة عمود exam_year</p>";
        }
        
        // إضافة عمود exam_session
        if (!$checkExamSession) {
            $pdo->exec("ALTER TABLE documents ADD COLUMN exam_session ENUM('الدورة العادية', 'دورة الاستدراك', 'دورة استثنائية') DEFAULT NULL AFTER exam_year");
            echo "<p>✅ تم إضافة عمود exam_session</p>";
        }
        
        // إضافة عمود branch
        if (!$checkBranch) {
            $pdo->exec("ALTER TABLE documents ADD COLUMN branch VARCHAR(100) DEFAULT NULL AFTER exam_session");
            echo "<p>✅ تم إضافة عمود branch</p>";
        }
        
        // تحديث enum للأنواع
        $pdo->exec("ALTER TABLE documents MODIFY COLUMN type ENUM('درس', 'امتحان', 'حل', 'تمرين', 'ملخص', 'موضوع شهادة', 'حل شهادة') NOT NULL");
        echo "<p>✅ تم تحديث أنواع الوثائق</p>";
        
        // إضافة بيانات تجريبية
        echo "<h3>إضافة بيانات تجريبية...</h3>";
        
        // تحديث بعض الوثائق لتصبح مواضيع شهادات
        $stmt = $pdo->prepare("
            UPDATE documents 
            SET type = 'موضوع شهادة', exam_year = '2024', exam_session = 'الدورة العادية'
            WHERE type = 'امتحان' AND subject_id IN (
                SELECT id FROM subjects WHERE level_id IN (4, 5)
            ) LIMIT 3
        ");
        $stmt->execute();
        echo "<p>✅ تم تحديث " . $stmt->rowCount() . " وثيقة لتصبح مواضيع شهادات</p>";
        
        // تحديث بعض الوثائق لتصبح حلول شهادات
        $stmt = $pdo->prepare("
            UPDATE documents 
            SET type = 'حل شهادة', exam_year = '2024', exam_session = 'الدورة العادية'
            WHERE type = 'حل' AND subject_id IN (
                SELECT id FROM subjects WHERE level_id IN (4, 5)
            ) LIMIT 3
        ");
        $stmt->execute();
        echo "<p>✅ تم تحديث " . $stmt->rowCount() . " وثيقة لتصبح حلول شهادات</p>";
        
        // إضافة بيانات للسنوات السابقة
        $years = ['2023', '2022', '2021'];
        $sessions = ['الدورة العادية', 'دورة الاستدراك'];
        
        foreach ($years as $index => $year) {
            $session = $sessions[$index % 2];
            $stmt = $pdo->prepare("
                UPDATE documents 
                SET exam_year = ?, exam_session = ?
                WHERE type IN ('موضوع شهادة', 'حل شهادة') AND exam_year IS NULL
                LIMIT 2
            ");
            $stmt->execute([$year, $session]);
            echo "<p>✅ تم إضافة بيانات لسنة $year</p>";
        }
        
        echo "<h3>✅ تم الانتهاء من التحديث بنجاح!</h3>";
        
    } else {
        echo "<h3>✅ جميع الأعمدة موجودة مسبقاً!</h3>";
    }
    
    // عرض الإحصائيات النهائية
    echo "<h3>الإحصائيات النهائية:</h3>";
    
    $stats = $pdo->query("
        SELECT 
            COUNT(*) as total_exam_docs,
            COUNT(CASE WHEN type = 'موضوع شهادة' THEN 1 END) as exam_subjects,
            COUNT(CASE WHEN type = 'حل شهادة' THEN 1 END) as exam_solutions,
            COUNT(DISTINCT exam_year) as available_years
        FROM documents 
        WHERE type IN ('موضوع شهادة', 'حل شهادة')
    ")->fetch();
    
    echo "<ul>";
    echo "<li>إجمالي وثائق الشهادات: " . $stats['total_exam_docs'] . "</li>";
    echo "<li>مواضيع الامتحانات: " . $stats['exam_subjects'] . "</li>";
    echo "<li>الحلول النموذجية: " . $stats['exam_solutions'] . "</li>";
    echo "<li>السنوات المتاحة: " . $stats['available_years'] . "</li>";
    echo "</ul>";
    
    // عرض عينة من الوثائق
    echo "<h3>عينة من وثائق الشهادات:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>العنوان</th><th>النوع</th><th>السنة</th><th>الدورة</th><th>المادة</th></tr>";
    
    $sampleDocs = $pdo->query("
        SELECT 
            d.title, d.type, d.exam_year, d.exam_session,
            s.name as subject_name
        FROM documents d
        JOIN subjects s ON d.subject_id = s.id
        WHERE d.type IN ('موضوع شهادة', 'حل شهادة')
        ORDER BY d.exam_year DESC, d.type
        LIMIT 10
    ")->fetchAll();
    
    foreach ($sampleDocs as $doc) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($doc['title']) . "</td>";
        echo "<td>" . $doc['type'] . "</td>";
        echo "<td>" . ($doc['exam_year'] ?: 'غير محدد') . "</td>";
        echo "<td>" . ($doc['exam_session'] ?: 'غير محدد') . "</td>";
        echo "<td>" . $doc['subject_name'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<br><p><strong>يمكنك الآن العودة إلى <a href='index.php'>الصفحة الرئيسية</a> لرؤية التحديثات.</strong></p>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ حدث خطأ أثناء التحديث:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>يرجى التأكد من:</p>";
    echo "<ul>";
    echo "<li>صحة اتصال قاعدة البيانات</li>";
    echo "<li>وجود صلاحيات كافية لتعديل الجداول</li>";
    echo "<li>عدم وجود قيود على قاعدة البيانات</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

th {
    background: #3498db;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
