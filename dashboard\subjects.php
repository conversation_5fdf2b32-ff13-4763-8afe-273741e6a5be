<?php
session_start();
require_once '../db/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// جلب المواد مع إحصائياتها
$subjects = $pdo->query("
    SELECT s.*, l.name as level_name,
           COUNT(d.id) as documents_count,
           SUM(d.download_count) as total_downloads,
           SUM(d.view_count) as total_views
    FROM subjects s
    JOIN levels l ON s.level_id = l.id
    LEFT JOIN documents d ON s.id = d.subject_id
    GROUP BY s.id
    ORDER BY l.id, s.name
")->fetchAll();

// جلب المستويات
$levels = getLevels();

// إحصائيات المواد
$stats = [
    'total_subjects' => $pdo->query("SELECT COUNT(*) FROM subjects")->fetchColumn(),
    'total_documents' => $pdo->query("SELECT COUNT(*) FROM documents")->fetchColumn(),
    'exam_subjects' => $pdo->query("SELECT COUNT(*) FROM subjects WHERE level_id IN (4,5)")->fetchColumn(),
    'regular_subjects' => $pdo->query("SELECT COUNT(*) FROM subjects WHERE level_id NOT IN (4,5)")->fetchColumn()
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المواد - لوحة التحكم</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: #fff;
        }

        .sidebar-menu i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            margin-right: 250px;
            padding: 2rem;
        }

        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .page-header h1 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
            font-size: 2rem;
        }

        .page-header p {
            margin: 0;
            color: #666;
            font-size: 1.1rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9rem;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .subject-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .subject-card:hover {
            transform: translateY(-5px);
        }

        .subject-header {
            padding: 1.5rem;
            background: linear-gradient(135deg, var(--subject-color, #3498db), color-mix(in srgb, var(--subject-color, #3498db) 80%, black));
            color: white;
            position: relative;
        }

        .subject-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .subject-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0 0 0.5rem 0;
        }

        .subject-level {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .subject-body {
            padding: 1.5rem;
        }

        .subject-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .subject-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .stat-item .number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .stat-item .label {
            font-size: 0.8rem;
            color: #666;
        }

        .subject-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
            justify-content: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .level-section {
            margin-bottom: 3rem;
        }

        .level-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .level-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .level-count {
            background: #3498db;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-right: 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .subjects-grid {
                grid-template-columns: 1fr;
            }

            .subject-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-graduation-cap"></i> لوحة التحكم</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="documents.php"><i class="fas fa-file-alt"></i> إدارة الملفات</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subjects.php" class="active"><i class="fas fa-book"></i> إدارة المواد</a></li>
                <li><a href="comments.php"><i class="fas fa-comments"></i> التعليقات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../index.php"><i class="fas fa-arrow-right"></i> العودة للموقع</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-book"></i> إدارة المواد الدراسية</h1>
                <p>إدارة وتنظيم المواد الدراسية لجميع المستويات</p>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="icon" style="color: #3498db;"><i class="fas fa-book"></i></div>
                    <div class="number"><?= number_format($stats['total_subjects']) ?></div>
                    <div class="label">إجمالي المواد</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #27ae60;"><i class="fas fa-file-alt"></i></div>
                    <div class="number"><?= number_format($stats['total_documents']) ?></div>
                    <div class="label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #e74c3c;"><i class="fas fa-certificate"></i></div>
                    <div class="number"><?= number_format($stats['exam_subjects']) ?></div>
                    <div class="label">مواد الشهادات</div>
                </div>
                <div class="stat-card">
                    <div class="icon" style="color: #f39c12;"><i class="fas fa-graduation-cap"></i></div>
                    <div class="number"><?= number_format($stats['regular_subjects']) ?></div>
                    <div class="label">المواد العادية</div>
                </div>
            </div>

            <!-- Subjects by Level -->
            <?php 
            $subjectsByLevel = [];
            foreach($subjects as $subject) {
                $subjectsByLevel[$subject['level_name']][] = $subject;
            }
            ?>

            <?php foreach($subjectsByLevel as $levelName => $levelSubjects): ?>
                <div class="level-section">
                    <div class="level-header">
                        <h2 class="level-title">
                            <i class="fas fa-layer-group"></i> <?= htmlspecialchars($levelName) ?>
                        </h2>
                        <span class="level-count"><?= count($levelSubjects) ?> مادة</span>
                    </div>

                    <div class="subjects-grid">
                        <?php foreach($levelSubjects as $subject): ?>
                            <div class="subject-card" style="--subject-color: <?= $subject['color'] ?>">
                                <div class="subject-header">
                                    <div class="subject-icon">
                                        <i class="<?= $subject['icon'] ?>"></i>
                                    </div>
                                    <h3 class="subject-title"><?= htmlspecialchars($subject['name']) ?></h3>
                                    <p class="subject-level"><?= htmlspecialchars($subject['level_name']) ?></p>
                                </div>

                                <div class="subject-body">
                                    <?php if($subject['description']): ?>
                                        <p class="subject-description"><?= htmlspecialchars($subject['description']) ?></p>
                                    <?php endif; ?>

                                    <div class="subject-stats">
                                        <div class="stat-item">
                                            <div class="number"><?= number_format($subject['documents_count']) ?></div>
                                            <div class="label">ملف</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="number"><?= number_format($subject['total_downloads'] ?? 0) ?></div>
                                            <div class="label">تحميل</div>
                                        </div>
                                        <div class="stat-item">
                                            <div class="number"><?= number_format($subject['total_views'] ?? 0) ?></div>
                                            <div class="label">مشاهدة</div>
                                        </div>
                                    </div>

                                    <div class="subject-actions">
                                        <a href="../subject.php?id=<?= $subject['id'] ?>" class="btn btn-primary" target="_blank">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="edit_subject.php?id=<?= $subject['id'] ?>" class="btn btn-warning">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </main>
    </div>
</body>
</html>
