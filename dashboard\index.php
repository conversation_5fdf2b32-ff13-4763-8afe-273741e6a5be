<?php
session_start();
require_once '../db/config.php';

// التحقق من صلاحيات المدير
if (!isAdmin()) {
    redirect('../index.php');
}

// جلب الإحصائيات
$stats = getSiteStats();

// جلب أحدث الأنشطة
$activitiesStmt = $pdo->prepare("
    SELECT a.*, u.full_name as user_name
    FROM activity_logs a
    LEFT JOIN users u ON a.user_id = u.id
    ORDER BY a.created_at DESC
    LIMIT 10
");
$activitiesStmt->execute();
$recentActivities = $activitiesStmt->fetchAll();

// جلب أحدث الوثائق المرفوعة
$recentDocsStmt = $pdo->prepare("
    SELECT d.*, s.name as subject_name, l.name as level_name, u.full_name as uploader_name
    FROM documents d
    JOIN subjects s ON d.subject_id = s.id
    JOIN levels l ON s.level_id = l.id
    LEFT JOIN users u ON d.uploaded_by = u.id
    ORDER BY d.upload_date DESC
    LIMIT 5
");
$recentDocsStmt->execute();
$recentDocuments = $recentDocsStmt->fetchAll();

// جلب المستخدمين الجدد
$newUsersStmt = $pdo->prepare("
    SELECT * FROM users 
    ORDER BY created_at DESC 
    LIMIT 5
");
$newUsersStmt->execute();
$newUsers = $newUsersStmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - منصة التعليم</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-layout {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: 100vh;
        }
        
        .sidebar {
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
        }
        
        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid #34495e;
            margin-bottom: 2rem;
        }
        
        .sidebar-header h2 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }
        
        .sidebar-header p {
            color: #bdc3c7;
            font-size: 0.9rem;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 2rem;
            color: #bdc3c7;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: #34495e;
            color: white;
            border-right: 4px solid #3498db;
        }
        
        .main-content {
            background: #f8f9fa;
            padding: 2rem;
            overflow-y: auto;
        }
        
        .dashboard-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .dashboard-header h1 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-header p {
            color: #666;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color, #3498db);
        }
        
        .stat-icon {
            font-size: 3rem;
            color: var(--card-color, #3498db);
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .content-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .card-header {
            background: #f8f9fa;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .card-header h3 {
            color: #2c3e50;
            margin: 0;
            font-size: 1.2rem;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #3498db;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            color: #2c3e50;
            margin-bottom: 0.3rem;
        }
        
        .activity-time {
            color: #666;
            font-size: 0.8rem;
        }
        
        .document-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .document-item:last-child {
            border-bottom: none;
        }
        
        .document-type-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #e74c3c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .document-info {
            flex: 1;
        }
        
        .document-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 0.3rem;
        }
        
        .document-meta {
            color: #666;
            font-size: 0.8rem;
        }
        
        .user-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .user-item:last-child {
            border-bottom: none;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #27ae60;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 0.3rem;
        }
        
        .user-role {
            color: #666;
            font-size: 0.8rem;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 3rem;
        }
        
        .action-btn {
            background: white;
            border: 2px solid #e1e8ed;
            padding: 1.5rem;
            border-radius: 10px;
            text-decoration: none;
            color: #2c3e50;
            text-align: center;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
        }
        
        .action-btn:hover {
            border-color: #3498db;
            background: #f8f9fa;
            transform: translateY(-2px);
        }
        
        .action-btn i {
            font-size: 2rem;
            color: #3498db;
        }
        
        @media (max-width: 768px) {
            .dashboard-layout {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                order: 2;
                padding: 1rem 0;
            }
            
            .sidebar-menu {
                display: flex;
                overflow-x: auto;
                padding: 0 1rem;
            }
            
            .sidebar-menu li {
                margin-bottom: 0;
                margin-left: 0.5rem;
                white-space: nowrap;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h2>
                <p>مرحباً، <?= $_SESSION['full_name'] ?></p>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php" class="active"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="documents.php"><i class="fas fa-file-alt"></i> إدارة الملفات</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subjects.php"><i class="fas fa-book"></i> إدارة المواد</a></li>
                <li><a href="comments.php"><i class="fas fa-comments"></i> التعليقات</a></li>
                <li><a href="settings.php"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../index.php"><i class="fas fa-arrow-right"></i> العودة للموقع</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم الرئيسية</h1>
                <p>نظرة عامة على إحصائيات الموقع والأنشطة الحديثة</p>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="add_document.php" class="action-btn">
                    <i class="fas fa-plus"></i>
                    <span>إضافة ملف جديد</span>
                </a>
                <a href="add_user.php" class="action-btn">
                    <i class="fas fa-user-plus"></i>
                    <span>إضافة مستخدم</span>
                </a>
                <a href="add_subject.php" class="action-btn">
                    <i class="fas fa-book-plus"></i>
                    <span>إضافة مادة</span>
                </a>
                <a href="reports.php" class="action-btn">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </a>
            </div>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card" style="--card-color: #3498db;">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-number"><?= $stats['total_documents'] ?></div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                
                <div class="stat-card" style="--card-color: #27ae60;">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-number"><?= $stats['total_users'] ?></div>
                    <div class="stat-label">المستخدمين</div>
                </div>
                
                <div class="stat-card" style="--card-color: #e74c3c;">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-number"><?= $stats['total_downloads'] ?></div>
                    <div class="stat-label">التحميلات</div>
                </div>
                
                <div class="stat-card" style="--card-color: #f39c12;">
                    <div class="stat-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-number"><?= $stats['lessons'] ?></div>
                    <div class="stat-label">الدروس</div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Recent Activities -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> الأنشطة الحديثة</h3>
                    </div>
                    <div class="card-body">
                        <?php foreach($recentActivities as $activity): ?>
                            <div class="activity-item">
                                <div class="activity-icon">
                                    <i class="fas fa-<?= getActivityIcon($activity['action']) ?>"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-text">
                                        <strong><?= $activity['user_name'] ?? 'مستخدم غير معروف' ?></strong>
                                        <?= getActivityText($activity['action']) ?>
                                    </div>
                                    <div class="activity-time">
                                        <?= date('Y-m-d H:i', strtotime($activity['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Recent Documents -->
                <div class="content-card">
                    <div class="card-header">
                        <h3><i class="fas fa-file-alt"></i> أحدث الملفات</h3>
                    </div>
                    <div class="card-body">
                        <?php foreach($recentDocuments as $doc): ?>
                            <div class="document-item">
                                <div class="document-type-icon">
                                    <i class="fas fa-<?= $doc['type'] == 'درس' ? 'book' : ($doc['type'] == 'امتحان' ? 'file-alt' : 'check-circle') ?>"></i>
                                </div>
                                <div class="document-info">
                                    <div class="document-title"><?= htmlspecialchars($doc['title']) ?></div>
                                    <div class="document-meta">
                                        <?= $doc['subject_name'] ?> • <?= $doc['level_name'] ?> • 
                                        <?= date('Y-m-d', strtotime($doc['upload_date'])) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- New Users -->
            <div class="content-card" style="margin-top: 2rem;">
                <div class="card-header">
                    <h3><i class="fas fa-user-plus"></i> المستخدمين الجدد</h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                        <?php foreach($newUsers as $user): ?>
                            <div class="user-item">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($user['full_name'], 0, 1)) ?>
                                </div>
                                <div class="user-info">
                                    <div class="user-name"><?= htmlspecialchars($user['full_name']) ?></div>
                                    <div class="user-role">
                                        <?= $user['role'] === 'admin' ? 'مدير' : ($user['role'] === 'teacher' ? 'معلم' : 'طالب') ?> • 
                                        <?= date('Y-m-d', strtotime($user['created_at'])) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../assets/js/script.js"></script>
</body>
</html>

<?php
function getActivityIcon($action) {
    $icons = [
        'login' => 'sign-in-alt',
        'logout' => 'sign-out-alt',
        'register' => 'user-plus',
        'upload' => 'upload',
        'download' => 'download',
        'rate_document' => 'star',
        'add_favorite' => 'heart',
        'remove_favorite' => 'heart-broken',
        'comment' => 'comment'
    ];
    
    return $icons[$action] ?? 'circle';
}

function getActivityText($action) {
    $texts = [
        'login' => 'سجل دخول للنظام',
        'logout' => 'سجل خروج من النظام',
        'register' => 'أنشأ حساب جديد',
        'upload' => 'رفع ملف جديد',
        'download' => 'حمل ملف',
        'rate_document' => 'قيم ملف',
        'add_favorite' => 'أضاف ملف للمفضلة',
        'remove_favorite' => 'أزال ملف من المفضلة',
        'comment' => 'أضاف تعليق'
    ];
    
    return $texts[$action] ?? 'قام بنشاط';
}
?>
