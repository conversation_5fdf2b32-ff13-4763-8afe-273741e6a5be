-- إضافة أقسام الشهادات الرسمية
-- تاريخ التحديث: 2024-12-19
-- الغرض: إضافة شهادة التعليم المتوسط وشهادة البكالوريا

-- إضافة المستويات الجديدة
INSERT INTO levels (name, description) VALUES
('شهادة التعليم المتوسط', 'مواضيع وحلول امتحانات شهادة التعليم المتوسط حسب السنوات'),
('شهادة البكالوريا', 'مواضيع وحلول امتحانات شهادة البكالوريا حسب السنوات والشعب');

-- إنشاء جدول السنوات الدراسية للشهادات
CREATE TABLE IF NOT EXISTS exam_years (
    id INT AUTO_INCREMENT PRIMARY KEY,
    year VARCHAR(10) NOT NULL,
    level_id INT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (level_id) REFERENCES levels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_year_level (year, level_id),
    INDEX idx_level_year (level_id, year),
    INDEX idx_active (is_active)
);

-- تحديث جدول الوثائق لإضافة حقول الشهادات
ALTER TABLE documents 
ADD COLUMN exam_year VARCHAR(10) DEFAULT NULL AFTER academic_year,
ADD COLUMN exam_session ENUM('الدورة العادية', 'دورة الاستدراك', 'دورة استثنائية') DEFAULT NULL AFTER exam_year,
ADD COLUMN branch VARCHAR(100) DEFAULT NULL AFTER exam_session;

-- تحديث enum للأنواع
ALTER TABLE documents 
MODIFY COLUMN type ENUM('درس', 'امتحان', 'حل', 'تمرين', 'ملخص', 'موضوع شهادة', 'حل شهادة') NOT NULL;

-- إدراج المواد الدراسية لشهادة التعليم المتوسط
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
(4, 'اللغة العربية', 'مواضيع وحلول اللغة العربية لشهادة التعليم المتوسط', 'fas fa-book-open', '#2ecc71'),
(4, 'الرياضيات', 'مواضيع وحلول الرياضيات لشهادة التعليم المتوسط', 'fas fa-calculator', '#e74c3c'),
(4, 'العلوم الفيزيائية', 'مواضيع وحلول العلوم الفيزيائية لشهادة التعليم المتوسط', 'fas fa-atom', '#9b59b6'),
(4, 'علوم الطبيعة والحياة', 'مواضيع وحلول علوم الطبيعة والحياة لشهادة التعليم المتوسط', 'fas fa-leaf', '#27ae60'),
(4, 'التاريخ والجغرافيا', 'مواضيع وحلول التاريخ والجغرافيا لشهادة التعليم المتوسط', 'fas fa-map', '#f39c12'),
(4, 'التربية الإسلامية', 'مواضيع وحلول التربية الإسلامية لشهادة التعليم المتوسط', 'fas fa-mosque', '#16a085'),
(4, 'اللغة الفرنسية', 'مواضيع وحلول اللغة الفرنسية لشهادة التعليم المتوسط', 'fas fa-language', '#3498db'),
(4, 'اللغة الإنجليزية', 'مواضيع وحلول اللغة الإنجليزية لشهادة التعليم المتوسط', 'fas fa-language', '#e67e22');

-- إدراج المواد الدراسية لشهادة البكالوريا
INSERT INTO subjects (level_id, name, description, icon, color) VALUES
-- شعبة العلوم التجريبية
(5, 'الرياضيات - علوم تجريبية', 'مواضيع وحلول الرياضيات لشعبة العلوم التجريبية', 'fas fa-calculator', '#e74c3c'),
(5, 'الفيزياء - علوم تجريبية', 'مواضيع وحلول الفيزياء لشعبة العلوم التجريبية', 'fas fa-atom', '#9b59b6'),
(5, 'علوم الطبيعة والحياة - علوم تجريبية', 'مواضيع وحلول علوم الطبيعة والحياة لشعبة العلوم التجريبية', 'fas fa-dna', '#27ae60'),
-- شعبة الرياضيات
(5, 'الرياضيات - رياضيات', 'مواضيع وحلول الرياضيات لشعبة الرياضيات', 'fas fa-calculator', '#c0392b'),
(5, 'الفيزياء - رياضيات', 'مواضيع وحلول الفيزياء لشعبة الرياضيات', 'fas fa-atom', '#8e44ad'),
-- شعبة تقني رياضي
(5, 'الرياضيات - تقني رياضي', 'مواضيع وحلول الرياضيات لشعبة تقني رياضي', 'fas fa-calculator', '#d35400'),
(5, 'الفيزياء - تقني رياضي', 'مواضيع وحلول الفيزياء لشعبة تقني رياضي', 'fas fa-atom', '#7f8c8d'),
(5, 'الهندسة المدنية', 'مواضيع وحلول الهندسة المدنية لشعبة تقني رياضي', 'fas fa-building', '#34495e'),
(5, 'الهندسة الميكانيكية', 'مواضيع وحلول الهندسة الميكانيكية لشعبة تقني رياضي', 'fas fa-cogs', '#95a5a6'),
(5, 'الهندسة الكهربائية', 'مواضيع وحلول الهندسة الكهربائية لشعبة تقني رياضي', 'fas fa-bolt', '#f1c40f'),
-- شعبة الآداب والفلسفة
(5, 'الفلسفة - آداب وفلسفة', 'مواضيع وحلول الفلسفة لشعبة الآداب والفلسفة', 'fas fa-brain', '#34495e'),
(5, 'اللغة العربية - آداب وفلسفة', 'مواضيع وحلول اللغة العربية لشعبة الآداب والفلسفة', 'fas fa-book-open', '#2ecc71'),
(5, 'التاريخ والجغرافيا - آداب وفلسفة', 'مواضيع وحلول التاريخ والجغرافيا لشعبة الآداب والفلسفة', 'fas fa-globe-americas', '#f39c12'),
-- شعبة اللغات الأجنبية
(5, 'اللغة الإنجليزية - لغات أجنبية', 'مواضيع وحلول اللغة الإنجليزية لشعبة اللغات الأجنبية', 'fas fa-language', '#e67e22'),
(5, 'اللغة الفرنسية - لغات أجنبية', 'مواضيع وحلول اللغة الفرنسية لشعبة اللغات الأجنبية', 'fas fa-language', '#3498db'),
(5, 'اللغة الألمانية - لغات أجنبية', 'مواضيع وحلول اللغة الألمانية لشعبة اللغات الأجنبية', 'fas fa-language', '#e74c3c'),
(5, 'اللغة الإسبانية - لغات أجنبية', 'مواضيع وحلول اللغة الإسبانية لشعبة اللغات الأجنبية', 'fas fa-language', '#f39c12'),
-- مواد مشتركة للبكالوريا
(5, 'اللغة العربية - مشترك', 'مواضيع وحلول اللغة العربية المشتركة لجميع الشعب', 'fas fa-book-open', '#2ecc71'),
(5, 'التربية الإسلامية - مشترك', 'مواضيع وحلول التربية الإسلامية المشتركة لجميع الشعب', 'fas fa-mosque', '#16a085'),
(5, 'اللغة الفرنسية - مشترك', 'مواضيع وحلول اللغة الفرنسية المشتركة لجميع الشعب', 'fas fa-language', '#3498db'),
(5, 'اللغة الإنجليزية - مشترك', 'مواضيع وحلول اللغة الإنجليزية المشتركة لجميع الشعب', 'fas fa-language', '#e67e22');

-- إدراج السنوات الدراسية لشهادة التعليم المتوسط
INSERT INTO exam_years (year, level_id, description) VALUES
('2024', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2024'),
('2023', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2023'),
('2022', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2022'),
('2021', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2021'),
('2020', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2020'),
('2019', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2019'),
('2018', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2018'),
('2017', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2017'),
('2016', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2016'),
('2015', 4, 'مواضيع وحلول شهادة التعليم المتوسط 2015');

-- إدراج السنوات الدراسية لشهادة البكالوريا
INSERT INTO exam_years (year, level_id, description) VALUES
('2024', 5, 'مواضيع وحلول شهادة البكالوريا 2024'),
('2023', 5, 'مواضيع وحلول شهادة البكالوريا 2023'),
('2022', 5, 'مواضيع وحلول شهادة البكالوريا 2022'),
('2021', 5, 'مواضيع وحلول شهادة البكالوريا 2021'),
('2020', 5, 'مواضيع وحلول شهادة البكالوريا 2020'),
('2019', 5, 'مواضيع وحلول شهادة البكالوريا 2019'),
('2018', 5, 'مواضيع وحلول شهادة البكالوريا 2018'),
('2017', 5, 'مواضيع وحلول شهادة البكالوريا 2017'),
('2016', 5, 'مواضيع وحلول شهادة البكالوريا 2016'),
('2015', 5, 'مواضيع وحلول شهادة البكالوريا 2015'),
('2014', 5, 'مواضيع وحلول شهادة البكالوريا 2014'),
('2013', 5, 'مواضيع وحلول شهادة البكالوريا 2013'),
('2012', 5, 'مواضيع وحلول شهادة البكالوريا 2012'),
('2011', 5, 'مواضيع وحلول شهادة البكالوريا 2011'),
('2010', 5, 'مواضيع وحلول شهادة البكالوريا 2010');

-- التحقق من التحديثات
SELECT 'المستويات المضافة:' as info;
SELECT id, name, description FROM levels WHERE id IN (4, 5);

SELECT 'المواد المضافة لشهادة التعليم المتوسط:' as info;
SELECT id, name FROM subjects WHERE level_id = 4;

SELECT 'المواد المضافة لشهادة البكالوريا:' as info;
SELECT id, name FROM subjects WHERE level_id = 5;

SELECT 'السنوات المضافة:' as info;
SELECT level_id, COUNT(*) as years_count FROM exam_years GROUP BY level_id;
