# دليل تثبيت قاعدة البيانات الجديدة 🗄️

## نظرة عامة
قاعدة البيانات الجديدة `database_new.sql` تحتوي على جميع المزايا المتقدمة للشهادات الرسمية مع تحسينات شاملة للأداء والوظائف.

## 🆕 **المزايا الجديدة:**

### 📊 **جداول محسنة:**
- **documents**: مع أعمدة الشهادات (exam_year, exam_session, branch)
- **exam_years**: جدول جديد لإدارة السنوات الدراسية
- **system_settings**: إعدادات النظام المتقدمة
- **activity_logs**: سجل النشاطات والتغييرات

### 🎓 **دعم الشهادات الكامل:**
- **شهادة التعليم المتوسط**: 8 مواد + 10 سنوات (2015-2024)
- **شهادة البكالوريا**: 20 مادة + 15 سنة (2010-2024)
- **تصنيف حسب الشعب**: علوم تجريبية، رياضيات، تقني رياضي، آداب، لغات
- **دورات متعددة**: عادية، استدراك، استثنائية

### 🔧 **تحسينات تقنية:**
- **فهارس محسنة**: لتسريع البحث والاستعلامات
- **Views جاهزة**: لتسهيل الاستعلامات المعقدة
- **Stored Procedures**: للعمليات المتكررة
- **Triggers**: لتحديث الإحصائيات تلقائياً

---

## 🚀 **طرق التثبيت:**

### 🎯 **الطريقة الأولى: مشروع جديد (مستحسنة)**

#### 1. إنشاء قاعدة بيانات جديدة:
```bash
# في phpMyAdmin أو MySQL command line
mysql -u root -p
```

```sql
-- حذف قاعدة البيانات القديمة (إذا كانت موجودة)
DROP DATABASE IF EXISTS school_platform;

-- إنشاء قاعدة بيانات جديدة
CREATE DATABASE school_platform CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
exit;
```

#### 2. استيراد قاعدة البيانات الجديدة:
```bash
# من مجلد المشروع
mysql -u root -p school_platform < db/database_new.sql
```

#### 3. التحقق من نجاح التثبيت:
- افتح `http://localhost/school-platform`
- يجب أن ترى الصفحة الرئيسية مع قسم الشهادات
- لا توجد رسائل خطأ

---

### 🔄 **الطريقة الثانية: ترقية مشروع موجود**

#### 1. أخذ نسخة احتياطية:
```bash
# نسخة احتياطية من قاعدة البيانات الحالية
mysqldump -u root -p school_platform > backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 2. تنفيذ ملف الترقية:
```bash
# استخدم ملف الإصلاح السريع
mysql -u root -p school_platform < db/quick_fix_exam_columns.sql
```

#### 3. أو استخدم ملف الإصلاح التفاعلي:
- افتح `http://localhost/school-platform/fix_database.php`
- اتبع التعليمات على الشاشة

---

### 🖥️ **الطريقة الثالثة: phpMyAdmin**

#### 1. افتح phpMyAdmin:
- اذهب إلى `http://localhost/phpmyadmin`

#### 2. إنشاء قاعدة بيانات جديدة:
- اضغط على "New" أو "جديد"
- اسم قاعدة البيانات: `school_platform`
- Collation: `utf8mb4_unicode_ci`
- اضغط "Create"

#### 3. استيراد الملف:
- اختر قاعدة البيانات `school_platform`
- اذهب إلى تبويب "Import" أو "استيراد"
- اختر ملف `db/database_new.sql`
- اضغط "Go" أو "تنفيذ"

---

## 📋 **محتويات قاعدة البيانات الجديدة:**

### 🏗️ **الجداول الأساسية:**
| الجدول | الوصف | عدد الأعمدة |
|---------|--------|-------------|
| `levels` | المستويات التعليمية | 5 |
| `subjects` | المواد الدراسية | 8 |
| `users` | المستخدمين | 22 |
| `documents` | الوثائق (محسن) | 25 |
| `exam_years` | سنوات الشهادات | 6 |

### 🔧 **الجداول المساعدة:**
| الجدول | الوصف | الغرض |
|---------|--------|-------|
| `ratings` | التقييمات | تقييم الوثائق |
| `comments` | التعليقات | تعليقات المستخدمين |
| `favorites` | المفضلة | حفظ الوثائق المهمة |
| `download_logs` | سجل التحميلات | تتبع التحميلات |
| `notifications` | الإشعارات | إشعارات المستخدمين |
| `system_settings` | إعدادات النظام | إعدادات الموقع |
| `activity_logs` | سجل النشاطات | تتبع العمليات |

### 📊 **البيانات المدرجة:**

#### 🎓 **المستويات (5):**
1. الابتدائي
2. المتوسط  
3. الثانوي
4. شهادة التعليم المتوسط
5. شهادة البكالوريا

#### 📚 **المواد (48 مادة):**
- **الابتدائي**: 6 مواد
- **المتوسط**: 8 مواد
- **الثانوي**: 10 مواد
- **شهادة التعليم المتوسط**: 8 مواد
- **شهادة البكالوريا**: 20 مادة (5 شعب + مواد مشتركة)

#### 📅 **السنوات الدراسية (25 سنة):**
- **شهادة التعليم المتوسط**: 2015-2024 (10 سنوات)
- **شهادة البكالوريا**: 2010-2024 (15 سنة)

#### 📄 **الوثائق التجريبية (30 وثيقة):**
- **مواضيع شهادة التعليم المتوسط**: 10 مواضيع
- **حلول شهادة التعليم المتوسط**: 10 حلول
- **مواضيع شهادة البكالوريا**: 10 مواضيع (شعب مختلفة)
- **حلول شهادة البكالوريا**: 10 حلول

---

## 🔍 **التحقق من نجاح التثبيت:**

### ✅ **اختبارات أساسية:**

#### 1. تحقق من الجداول:
```sql
SHOW TABLES;
-- يجب أن ترى 12 جدول
```

#### 2. تحقق من المستويات:
```sql
SELECT * FROM levels;
-- يجب أن ترى 5 مستويات
```

#### 3. تحقق من مواد الشهادات:
```sql
SELECT COUNT(*) FROM subjects WHERE level_id IN (4,5);
-- يجب أن ترى 28 مادة
```

#### 4. تحقق من وثائق الشهادات:
```sql
SELECT COUNT(*) FROM documents WHERE type IN ('موضوع شهادة', 'حل شهادة');
-- يجب أن ترى 30 وثيقة
```

### 🌐 **اختبارات الموقع:**

#### 1. الصفحة الرئيسية:
- افتح `http://localhost/school-platform`
- يجب أن ترى قسم "الشهادات الرسمية"
- يجب أن ترى قسم "مواضيع وحلول الشهادات"

#### 2. صفحات الشهادات:
- `http://localhost/school-platform/exam_level.php?id=4` (BEM)
- `http://localhost/school-platform/exam_level.php?id=5` (BAC)

#### 3. البحث والفلترة:
- جرب البحث بسنة معينة
- جرب الفلترة حسب النوع

---

## 🔧 **إعدادات ما بعد التثبيت:**

### 👤 **حساب المدير الافتراضي:**
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `password` (يجب تغييرها)
- **البريد الإلكتروني**: `<EMAIL>`

### 📁 **إنشاء مجلدات الرفع:**
```bash
# إنشاء مجلدات الرفع
mkdir -p uploads/bem/2024
mkdir -p uploads/bem/2023
mkdir -p uploads/bac/2024
mkdir -p uploads/bac/2023
chmod -R 755 uploads/
```

### ⚙️ **تحديث إعدادات النظام:**
```sql
-- تحديث اسم الموقع
UPDATE system_settings SET setting_value = 'اسم مدرستك' WHERE setting_key = 'site_name';

-- تحديث بريد التواصل
UPDATE system_settings SET setting_value = '<EMAIL>' WHERE setting_key = 'contact_email';
```

---

## 🚨 **استكشاف الأخطاء:**

### ❌ **خطأ في الاتصال:**
```
Connection refused
```
**الحل:**
- تأكد من تشغيل MySQL
- تحقق من بيانات الاتصال في `db/config.php`

### ❌ **خطأ في الصلاحيات:**
```
Access denied
```
**الحل:**
- تأكد من صلاحيات المستخدم
- استخدم مستخدم root للتثبيت

### ❌ **خطأ في الترميز:**
```
Incorrect string value
```
**الحل:**
- تأكد من استخدام `utf8mb4`
- تحقق من إعدادات MySQL

### ❌ **ملفات مفقودة:**
```
File not found
```
**الحل:**
- تأكد من وجود جميع ملفات المشروع
- تحقق من مسارات الملفات

---

## 📞 **الدعم والمساعدة:**

### 🔧 **للمساعدة التقنية:**
1. تحقق من ملف `error.log`
2. فعل وضع التطوير في PHP
3. استخدم أدوات المطور في المتصفح

### 📚 **الموارد المفيدة:**
- [دليل MySQL](https://dev.mysql.com/doc/)
- [دليل PHP](https://www.php.net/manual/)
- [دليل phpMyAdmin](https://docs.phpmyadmin.net/)

---

**بعد التثبيت الناجح، ستحصل على منصة تعليمية متكاملة مع دعم كامل للشهادات الرسمية! 🎉**
