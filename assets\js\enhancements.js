// ملف التحسينات المتقدمة للتفاعل والحركة
document.addEventListener('DOMContentLoaded', function() {
    
    // تفعيل تأثيرات الظهور المتقدمة
    function initAdvancedAnimations() {
        const animatedElements = document.querySelectorAll('.zoom-in, .slide-in-top, .slide-in-bottom, .bounce-in, .rotate-in, .flip-in-x');
        
        const animationObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.animationPlayState = 'running';
                    animationObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });

        animatedElements.forEach(element => {
            element.style.animationPlayState = 'paused';
            animationObserver.observe(element);
        });
    }

    // تأثيرات الماوس المتقدمة
    function initMouseEffects() {
        const interactiveElements = document.querySelectorAll('.interactive-element');
        
        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', function(e) {
                this.style.transform = 'translateY(-8px) scale(1.03)';
                this.style.boxShadow = '0 15px 35px rgba(0,0,0,0.2), 0 0 25px rgba(52, 152, 219, 0.4)';
                this.style.zIndex = '10';
            });
            
            element.addEventListener('mouseleave', function(e) {
                this.style.transform = 'translateY(0) scale(1)';
                this.style.boxShadow = '';
                this.style.zIndex = '';
            });
            
            // تأثير تتبع الماوس
            element.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                this.style.transform = `translateY(-8px) scale(1.03) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
            });
        });
    }

    // تأثيرات الأزرار المتقدمة
    function initAdvancedButtons() {
        const buttons = document.querySelectorAll('.btn, button');
        
        buttons.forEach(button => {
            // إضافة تأثير الضوء المتحرك
            button.addEventListener('mouseenter', function() {
                if (!this.querySelector('.light-effect')) {
                    const lightEffect = document.createElement('div');
                    lightEffect.className = 'light-effect';
                    lightEffect.style.cssText = `
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
                        transition: left 0.6s ease;
                        pointer-events: none;
                    `;
                    this.style.position = 'relative';
                    this.style.overflow = 'hidden';
                    this.appendChild(lightEffect);
                    
                    setTimeout(() => {
                        lightEffect.style.left = '100%';
                    }, 50);
                }
            });
            
            // تأثير النقر المتقدم
            button.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // إنشاء تأثير الانفجار
                const explosion = document.createElement('div');
                explosion.style.cssText = `
                    position: absolute;
                    left: ${x}px;
                    top: ${y}px;
                    width: 0;
                    height: 0;
                    border-radius: 50%;
                    background: radial-gradient(circle, rgba(255,255,255,0.8), transparent);
                    transform: translate(-50%, -50%);
                    animation: explode 0.6s ease-out;
                    pointer-events: none;
                `;
                
                this.appendChild(explosion);
                
                setTimeout(() => {
                    explosion.remove();
                }, 600);
            });
        });
    }

    // تأثيرات التمرير المتقدمة
    function initScrollEffects() {
        let ticking = false;
        
        function updateScrollEffects() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            // تأثير البارالاكس للخلفية
            const heroSection = document.querySelector('.hero');
            if (heroSection) {
                heroSection.style.transform = `translateY(${rate}px)`;
            }
            
            // تأثير الشفافية للعناصر
            const fadeElements = document.querySelectorAll('.fade-on-scroll');
            fadeElements.forEach(element => {
                const elementTop = element.offsetTop;
                const elementHeight = element.offsetHeight;
                const windowHeight = window.innerHeight;
                
                if (scrolled > elementTop - windowHeight + elementHeight / 2) {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                } else {
                    element.style.opacity = '0';
                    element.style.transform = 'translateY(30px)';
                }
            });
            
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollEffects);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick);
    }

    // تأثيرات الكتابة المتقدمة
    function initTypewriterEffect() {
        const typewriterElements = document.querySelectorAll('.typewriter');
        
        typewriterElements.forEach(element => {
            const text = element.textContent;
            element.textContent = '';
            element.style.borderRight = '2px solid';
            element.style.animation = 'blink 1s infinite';
            
            let i = 0;
            const typeInterval = setInterval(() => {
                element.textContent += text.charAt(i);
                i++;
                if (i > text.length) {
                    clearInterval(typeInterval);
                    element.style.borderRight = 'none';
                    element.style.animation = 'none';
                }
            }, 80);
        });
    }

    // تأثيرات الجسيمات التفاعلية
    function initInteractiveParticles() {
        const particleContainers = document.querySelectorAll('.particles-container');
        
        particleContainers.forEach(container => {
            container.addEventListener('mousemove', function(e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                // إنشاء جسيمات تفاعلية
                for (let i = 0; i < 3; i++) {
                    const particle = document.createElement('div');
                    particle.style.cssText = `
                        position: absolute;
                        left: ${x}px;
                        top: ${y}px;
                        width: 4px;
                        height: 4px;
                        background: rgba(255,255,255,0.8);
                        border-radius: 50%;
                        pointer-events: none;
                        animation: particleBurst 1s ease-out forwards;
                        transform: translate(-50%, -50%);
                    `;
                    
                    this.appendChild(particle);
                    
                    setTimeout(() => {
                        particle.remove();
                    }, 1000);
                }
            });
        });
    }

    // تأثيرات الصوت البصرية
    function initAudioVisualEffects() {
        const audioElements = document.querySelectorAll('audio, video');
        
        audioElements.forEach(element => {
            element.addEventListener('play', function() {
                this.parentElement.classList.add('playing');
            });
            
            element.addEventListener('pause', function() {
                this.parentElement.classList.remove('playing');
            });
        });
    }

    // إضافة CSS للتأثيرات الجديدة
    const enhancementStyles = document.createElement('style');
    enhancementStyles.textContent = `
        @keyframes explode {
            to {
                width: 200px;
                height: 200px;
                opacity: 0;
            }
        }
        
        @keyframes particleBurst {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(0) translateY(-50px);
                opacity: 0;
            }
        }
        
        @keyframes blink {
            0%, 50% { border-color: transparent; }
            51%, 100% { border-color: currentColor; }
        }
        
        .fade-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .playing {
            animation: pulse 1s infinite;
        }
        
        .gpu-accelerated {
            transform: translateZ(0);
            will-change: transform, opacity;
        }
    `;
    document.head.appendChild(enhancementStyles);

    // تفعيل جميع التأثيرات
    initAdvancedAnimations();
    initMouseEffects();
    initAdvancedButtons();
    initScrollEffects();
    initTypewriterEffect();
    initInteractiveParticles();
    initAudioVisualEffects();
    
    // إضافة مؤشر تحميل متقدم
    window.addEventListener('beforeunload', function() {
        document.body.style.opacity = '0.7';
        document.body.style.pointerEvents = 'none';
    });
    
    console.log('🎨 تم تفعيل جميع التحسينات المتقدمة بنجاح!');
});
