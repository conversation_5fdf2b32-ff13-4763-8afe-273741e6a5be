<?php
// ملف إصلاح قاعدة البيانات - إضافة جداول الإعدادات والجداول المفقودة
require_once 'db/config.php';

echo "<h2>إصلاح قاعدة البيانات - إضافة الجداول المفقودة</h2>";

try {
    // التحقق من وجود الجداول
    $tables_to_check = [
        'system_settings',
        'activity_logs', 
        'notifications',
        'favorites',
        'ratings',
        'comments',
        'download_logs'
    ];
    
    echo "<h3>فحص الجداول الموجودة:</h3>";
    echo "<ul>";
    
    $missing_tables = [];
    foreach ($tables_to_check as $table) {
        $check = $pdo->query("SHOW TABLES LIKE '$table'")->rowCount();
        if ($check) {
            echo "<li style='color: green;'>✅ $table - موجود</li>";
        } else {
            echo "<li style='color: red;'>❌ $table - مفقود</li>";
            $missing_tables[] = $table;
        }
    }
    echo "</ul>";
    
    if (!empty($missing_tables)) {
        echo "<h3>إنشاء الجداول المفقودة...</h3>";
        
        // إنشاء جدول إعدادات النظام
        if (in_array('system_settings', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE system_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
                    description TEXT,
                    is_public BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_key (setting_key),
                    INDEX idx_public (is_public)
                )
            ");
            echo "<p>✅ تم إنشاء جدول system_settings</p>";
            
            // إدراج الإعدادات الافتراضية
            $settings = [
                ['site_name', 'منصة التعليم', 'string', 'اسم الموقع', 1],
                ['site_description', 'منصة تعليمية شاملة للمراحل الدراسية والشهادات الرسمية', 'string', 'وصف الموقع', 1],
                ['site_keywords', 'تعليم,مدرسة,شهادات,امتحانات,دروس', 'string', 'كلمات مفتاحية للموقع', 1],
                ['admin_email', '<EMAIL>', 'string', 'بريد المدير الإلكتروني', 0],
                ['contact_email', '<EMAIL>', 'string', 'بريد التواصل', 1],
                ['contact_phone', '', 'string', 'رقم الهاتف للتواصل', 1],
                ['contact_address', '', 'string', 'عنوان المؤسسة', 1],
                ['max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف بالبايت (10MB)', 0],
                ['allowed_file_types', 'pdf,doc,docx,ppt,pptx,xls,xlsx,jpg,jpeg,png,gif', 'string', 'أنواع الملفات المسموحة', 0],
                ['enable_registration', 'true', 'boolean', 'تفعيل التسجيل الجديد', 0],
                ['enable_comments', 'true', 'boolean', 'تفعيل التعليقات', 0],
                ['enable_ratings', 'true', 'boolean', 'تفعيل التقييمات', 0],
                ['items_per_page', '12', 'number', 'عدد العناصر في الصفحة الواحدة', 0],
                ['maintenance_mode', 'false', 'boolean', 'وضع الصيانة', 0],
                ['google_analytics_id', '', 'string', 'معرف Google Analytics', 0],
                ['facebook_url', '', 'string', 'رابط صفحة Facebook', 1],
                ['twitter_url', '', 'string', 'رابط صفحة Twitter', 1],
                ['youtube_url', '', 'string', 'رابط قناة YouTube', 1]
            ];
            
            $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES (?, ?, ?, ?, ?)");
            foreach ($settings as $setting) {
                $stmt->execute($setting);
            }
            echo "<p>✅ تم إدراج " . count($settings) . " إعداد افتراضي</p>";
        }
        
        // إنشاء جدول سجل النشاطات
        if (in_array('activity_logs', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE activity_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT DEFAULT NULL,
                    action VARCHAR(100) NOT NULL,
                    table_name VARCHAR(50) DEFAULT NULL,
                    record_id INT DEFAULT NULL,
                    old_values JSON DEFAULT NULL,
                    new_values JSON DEFAULT NULL,
                    ip_address VARCHAR(45) DEFAULT NULL,
                    user_agent TEXT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX idx_user_id (user_id),
                    INDEX idx_action (action),
                    INDEX idx_table_record (table_name, record_id),
                    INDEX idx_created_at (created_at)
                )
            ");
            echo "<p>✅ تم إنشاء جدول activity_logs</p>";
        }
        
        // إنشاء جدول الإشعارات
        if (in_array('notifications', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE notifications (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    title VARCHAR(200) NOT NULL,
                    message TEXT NOT NULL,
                    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
                    is_read BOOLEAN DEFAULT FALSE,
                    action_url VARCHAR(500) DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    INDEX idx_user_read (user_id, is_read),
                    INDEX idx_created_at (created_at)
                )
            ");
            echo "<p>✅ تم إنشاء جدول notifications</p>";
        }
        
        // إنشاء جدول المفضلة
        if (in_array('favorites', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE favorites (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    document_id INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user_document (user_id, document_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_document_id (document_id)
                )
            ");
            echo "<p>✅ تم إنشاء جدول favorites</p>";
        }
        
        // إنشاء جدول التقييمات
        if (in_array('ratings', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE ratings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    document_id INT NOT NULL,
                    user_id INT NOT NULL,
                    rating TINYINT CHECK (rating >= 1 AND rating <= 5),
                    review TEXT DEFAULT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_user_document (user_id, document_id),
                    INDEX idx_document_id (document_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_rating (rating)
                )
            ");
            echo "<p>✅ تم إنشاء جدول ratings</p>";
        }
        
        // إنشاء جدول التعليقات
        if (in_array('comments', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE comments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    document_id INT NOT NULL,
                    user_id INT NOT NULL,
                    parent_id INT DEFAULT NULL,
                    content TEXT NOT NULL,
                    is_approved BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
                    INDEX idx_document_id (document_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_parent_id (parent_id),
                    INDEX idx_approved (is_approved)
                )
            ");
            echo "<p>✅ تم إنشاء جدول comments</p>";
        }
        
        // إنشاء جدول سجل التحميلات
        if (in_array('download_logs', $missing_tables)) {
            $pdo->exec("
                CREATE TABLE download_logs (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    document_id INT NOT NULL,
                    user_id INT DEFAULT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT DEFAULT NULL,
                    download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
                    INDEX idx_document_id (document_id),
                    INDEX idx_user_id (user_id),
                    INDEX idx_download_date (download_date),
                    INDEX idx_ip_address (ip_address)
                )
            ");
            echo "<p>✅ تم إنشاء جدول download_logs</p>";
        }
        
        echo "<h3>✅ تم الانتهاء من إنشاء جميع الجداول المفقودة!</h3>";
        
    } else {
        echo "<h3>✅ جميع الجداول موجودة مسبقاً!</h3>";
    }
    
    // عرض الإحصائيات النهائية
    echo "<h3>الإحصائيات النهائية:</h3>";
    
    $final_stats = [];
    foreach ($tables_to_check as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM $table")->fetchColumn();
            $final_stats[$table] = $count;
        } catch (Exception $e) {
            $final_stats[$table] = 'خطأ';
        }
    }
    
    echo "<ul>";
    foreach ($final_stats as $table => $count) {
        echo "<li><strong>$table</strong>: $count سجل</li>";
    }
    echo "</ul>";
    
    // عرض عينة من الإعدادات
    if (isset($final_stats['system_settings']) && $final_stats['system_settings'] > 0) {
        echo "<h3>عينة من الإعدادات:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>المفتاح</th><th>القيمة</th><th>النوع</th><th>الوصف</th></tr>";
        
        $sampleSettings = $pdo->query("SELECT setting_key, setting_value, setting_type, description FROM system_settings LIMIT 10")->fetchAll();
        
        foreach ($sampleSettings as $setting) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($setting['setting_key']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($setting['setting_value'], 0, 50)) . (strlen($setting['setting_value']) > 50 ? '...' : '') . "</td>";
            echo "<td>" . $setting['setting_type'] . "</td>";
            echo "<td>" . htmlspecialchars($setting['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<br><p><strong>يمكنك الآن الوصول إلى:</strong></p>";
    echo "<ul>";
    echo "<li><a href='dashboard/settings.php'>صفحة الإعدادات</a></li>";
    echo "<li><a href='dashboard/documents.php'>إدارة الملفات</a></li>";
    echo "<li><a href='dashboard/users.php'>إدارة المستخدمين</a></li>";
    echo "<li><a href='dashboard/comments.php'>إدارة التعليقات</a></li>";
    echo "<li><a href='index.php'>الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ حدث خطأ أثناء التحديث:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>يرجى التأكد من:</p>";
    echo "<ul>";
    echo "<li>صحة اتصال قاعدة البيانات</li>";
    echo "<li>وجود صلاحيات كافية لإنشاء الجداول</li>";
    echo "<li>عدم وجود قيود على قاعدة البيانات</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
}

h2, h3 {
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

table {
    background: white;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

th {
    background: #3498db;
    color: white;
    padding: 10px;
}

td {
    padding: 8px;
    border-bottom: 1px solid #eee;
}

p {
    background: white;
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
