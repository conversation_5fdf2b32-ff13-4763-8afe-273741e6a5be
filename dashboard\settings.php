<?php
session_start();
require_once '../db/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit;
}

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        foreach ($_POST as $key => $value) {
            if ($key !== 'submit') {
                $stmt = $pdo->prepare("
                    INSERT INTO system_settings (setting_key, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$key, $value]);
            }
        }
        $success = 'تم حفظ الإعدادات بنجاح!';
    } catch (Exception $e) {
        $error = 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage();
    }
}

// جلب الإعدادات الحالية
$settings = [];
$stmt = $pdo->query("SELECT setting_key, setting_value FROM system_settings");
while ($row = $stmt->fetch()) {
    $settings[$row['setting_key']] = $row['setting_value'];
}

// الإعدادات الافتراضية
$defaultSettings = [
    'site_name' => 'منصة التعليم',
    'site_description' => 'منصة تعليمية شاملة للمراحل الدراسية والشهادات الرسمية',
    'site_keywords' => 'تعليم,مدرسة,شهادات,امتحانات,دروس',
    'admin_email' => '<EMAIL>',
    'contact_email' => '<EMAIL>',
    'contact_phone' => '',
    'contact_address' => '',
    'max_file_size' => '10485760',
    'allowed_file_types' => 'pdf,doc,docx,ppt,pptx,xls,xlsx,jpg,jpeg,png,gif',
    'enable_registration' => 'true',
    'enable_comments' => 'true',
    'enable_ratings' => 'true',
    'items_per_page' => '12',
    'maintenance_mode' => 'false',
    'google_analytics_id' => '',
    'facebook_url' => '',
    'twitter_url' => '',
    'youtube_url' => ''
];

// دمج الإعدادات الافتراضية مع المحفوظة
foreach ($defaultSettings as $key => $defaultValue) {
    if (!isset($settings[$key])) {
        $settings[$key] = $defaultValue;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - لوحة التحكم</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: #fff;
        }

        .sidebar-menu i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }

        .main-content {
            flex: 1;
            margin-right: 250px;
            padding: 2rem;
        }

        .page-header {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .page-header h1 {
            margin: 0 0 0.5rem 0;
            color: #2c3e50;
            font-size: 2rem;
        }

        .page-header p {
            margin: 0;
            color: #666;
            font-size: 1.1rem;
        }

        .settings-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .settings-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e8ed;
        }

        .tab-button {
            flex: 1;
            padding: 1rem 2rem;
            background: none;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            color: #3498db;
            border-bottom-color: #3498db;
            background: white;
        }

        .tab-content {
            display: none;
            padding: 2rem;
        }

        .tab-content.active {
            display: block;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e1e8ed;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-input,
        .form-textarea,
        .form-select {
            padding: 0.75rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-help {
            font-size: 0.85rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
        }

        .btn {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .form-actions {
            padding: 2rem;
            background: #f8f9fa;
            border-top: 1px solid #e1e8ed;
            text-align: center;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-right: 0;
            }

            .settings-tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-graduation-cap"></i> لوحة التحكم</h2>
            </div>
            
            <ul class="sidebar-menu">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="documents.php"><i class="fas fa-file-alt"></i> إدارة الملفات</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> إدارة المستخدمين</a></li>
                <li><a href="subjects.php"><i class="fas fa-book"></i> إدارة المواد</a></li>
                <li><a href="comments.php"><i class="fas fa-comments"></i> التعليقات</a></li>
                <li><a href="settings.php" class="active"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="../index.php"><i class="fas fa-arrow-right"></i> العودة للموقع</a></li>
                <li><a href="../logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                <p>إدارة وتخصيص إعدادات المنصة التعليمية</p>
            </div>

            <!-- Alerts -->
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?= $success ?>
                </div>
            <?php endif; ?>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?= $error ?>
                </div>
            <?php endif; ?>

            <!-- Settings Form -->
            <form method="POST" class="settings-form">
                <!-- Tabs -->
                <div class="settings-tabs">
                    <button type="button" class="tab-button active" onclick="showTab('general')">
                        <i class="fas fa-globe"></i> عام
                    </button>
                    <button type="button" class="tab-button" onclick="showTab('files')">
                        <i class="fas fa-file"></i> الملفات
                    </button>
                    <button type="button" class="tab-button" onclick="showTab('features')">
                        <i class="fas fa-puzzle-piece"></i> المزايا
                    </button>
                    <button type="button" class="tab-button" onclick="showTab('social')">
                        <i class="fas fa-share-alt"></i> التواصل
                    </button>
                </div>

                <!-- General Settings -->
                <div id="general" class="tab-content active">
                    <div class="form-section">
                        <h3><i class="fas fa-info-circle"></i> معلومات الموقع</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="site_name">اسم الموقع</label>
                                <input type="text" id="site_name" name="site_name" class="form-input" 
                                       value="<?= htmlspecialchars($settings['site_name']) ?>" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="admin_email">بريد المدير</label>
                                <input type="email" id="admin_email" name="admin_email" class="form-input" 
                                       value="<?= htmlspecialchars($settings['admin_email']) ?>" required>
                            </div>
                            
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="site_description">وصف الموقع</label>
                                <textarea id="site_description" name="site_description" class="form-textarea"><?= htmlspecialchars($settings['site_description']) ?></textarea>
                            </div>
                            
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="site_keywords">الكلمات المفتاحية</label>
                                <input type="text" id="site_keywords" name="site_keywords" class="form-input" 
                                       value="<?= htmlspecialchars($settings['site_keywords']) ?>">
                                <div class="form-help">افصل بين الكلمات بفاصلة</div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-phone"></i> معلومات التواصل</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="contact_email">بريد التواصل</label>
                                <input type="email" id="contact_email" name="contact_email" class="form-input" 
                                       value="<?= htmlspecialchars($settings['contact_email']) ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="contact_phone">رقم الهاتف</label>
                                <input type="text" id="contact_phone" name="contact_phone" class="form-input" 
                                       value="<?= htmlspecialchars($settings['contact_phone']) ?>">
                            </div>
                            
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="contact_address">العنوان</label>
                                <textarea id="contact_address" name="contact_address" class="form-textarea"><?= htmlspecialchars($settings['contact_address']) ?></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Files Settings -->
                <div id="files" class="tab-content">
                    <div class="form-section">
                        <h3><i class="fas fa-upload"></i> إعدادات الملفات</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="max_file_size">الحد الأقصى لحجم الملف (بايت)</label>
                                <input type="number" id="max_file_size" name="max_file_size" class="form-input" 
                                       value="<?= htmlspecialchars($settings['max_file_size']) ?>" min="1048576">
                                <div class="form-help">القيمة الحالية: <?= round($settings['max_file_size'] / 1048576, 2) ?> ميجابايت</div>
                            </div>
                            
                            <div class="form-group">
                                <label for="items_per_page">عدد العناصر في الصفحة</label>
                                <input type="number" id="items_per_page" name="items_per_page" class="form-input" 
                                       value="<?= htmlspecialchars($settings['items_per_page']) ?>" min="1" max="50">
                            </div>
                            
                            <div class="form-group" style="grid-column: 1 / -1;">
                                <label for="allowed_file_types">أنواع الملفات المسموحة</label>
                                <input type="text" id="allowed_file_types" name="allowed_file_types" class="form-input" 
                                       value="<?= htmlspecialchars($settings['allowed_file_types']) ?>">
                                <div class="form-help">افصل بين الأنواع بفاصلة (مثال: pdf,doc,jpg)</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Settings -->
                <div id="features" class="tab-content">
                    <div class="form-section">
                        <h3><i class="fas fa-toggle-on"></i> تفعيل المزايا</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="enable_registration" name="enable_registration" 
                                           value="true" <?= $settings['enable_registration'] === 'true' ? 'checked' : '' ?>>
                                    <label for="enable_registration">تفعيل التسجيل الجديد</label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="enable_comments" name="enable_comments" 
                                           value="true" <?= $settings['enable_comments'] === 'true' ? 'checked' : '' ?>>
                                    <label for="enable_comments">تفعيل التعليقات</label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="enable_ratings" name="enable_ratings" 
                                           value="true" <?= $settings['enable_ratings'] === 'true' ? 'checked' : '' ?>>
                                    <label for="enable_ratings">تفعيل التقييمات</label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                                           value="true" <?= $settings['maintenance_mode'] === 'true' ? 'checked' : '' ?>>
                                    <label for="maintenance_mode">وضع الصيانة</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h3><i class="fas fa-chart-line"></i> التتبع والتحليل</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="google_analytics_id">معرف Google Analytics</label>
                                <input type="text" id="google_analytics_id" name="google_analytics_id" class="form-input" 
                                       value="<?= htmlspecialchars($settings['google_analytics_id']) ?>" 
                                       placeholder="G-XXXXXXXXXX">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Settings -->
                <div id="social" class="tab-content">
                    <div class="form-section">
                        <h3><i class="fas fa-share-alt"></i> روابط التواصل الاجتماعي</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="facebook_url">رابط Facebook</label>
                                <input type="url" id="facebook_url" name="facebook_url" class="form-input" 
                                       value="<?= htmlspecialchars($settings['facebook_url']) ?>" 
                                       placeholder="https://facebook.com/yourpage">
                            </div>
                            
                            <div class="form-group">
                                <label for="twitter_url">رابط Twitter</label>
                                <input type="url" id="twitter_url" name="twitter_url" class="form-input" 
                                       value="<?= htmlspecialchars($settings['twitter_url']) ?>" 
                                       placeholder="https://twitter.com/youraccount">
                            </div>
                            
                            <div class="form-group">
                                <label for="youtube_url">رابط YouTube</label>
                                <input type="url" id="youtube_url" name="youtube_url" class="form-input" 
                                       value="<?= htmlspecialchars($settings['youtube_url']) ?>" 
                                       placeholder="https://youtube.com/yourchannel">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" name="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                </div>
            </form>
        </main>
    </div>

    <script>
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            
            // تفعيل الزر المحدد
            event.target.classList.add('active');
        }

        // إضافة hidden input للـ checkboxes غير المحددة
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const hiddenInput = document.querySelector(`input[name="${this.name}"][type="hidden"]`);
                if (hiddenInput) {
                    hiddenInput.remove();
                }
                
                if (!this.checked) {
                    const hidden = document.createElement('input');
                    hidden.type = 'hidden';
                    hidden.name = this.name;
                    hidden.value = 'false';
                    this.parentNode.appendChild(hidden);
                }
            });
            
            // تشغيل الحدث عند التحميل
            checkbox.dispatchEvent(new Event('change'));
        });
    </script>
</body>
</html>
