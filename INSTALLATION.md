# 🚀 دليل التثبيت السريع - منصة التعليم

## المتطلبات الأساسية

- **XAMPP** (يتضمن Apache + MySQL + PHP)
- **متصفح ويب** حديث
- **مساحة تخزين** 500MB على الأقل

## خطوات التثبيت

### 1️⃣ تحميل وتثبيت XAMPP

1. اذهب إلى [موقع XAMPP الرسمي](https://www.apachefriends.org/)
2. حمل النسخة المناسبة لنظام التشغيل
3. ثبت XAMPP في المجلد الافتراضي `C:\xampp`

### 2️⃣ تشغيل الخدمات

1. افتح **XAMPP Control Panel**
2. اضغط **Start** بجانب **Apache**
3. اضغط **Start** بجانب **MySQL**
4. تأكد من ظهور اللون الأخضر بجانب الخدمتين

### 3️⃣ نسخ ملفات المشروع

```bash
# انسخ مجلد المشروع إلى htdocs
نسخ من: C:\Users\<USER>\Desktop\DRERRASSA
إلى: C:\xampp\htdocs\school-platform
```

### 4️⃣ إنشاء قاعدة البيانات

1. افتح المتصفح واذهب إلى: `http://localhost/phpmyadmin`
2. اضغط على **"قواعد البيانات"** أو **"Databases"**
3. أدخل اسم قاعدة البيانات: `school_platform`
4. اختر **utf8mb4_unicode_ci** كترميز
5. اضغط **"إنشاء"** أو **"Create"**

### 5️⃣ استيراد هيكل قاعدة البيانات

1. اختر قاعدة البيانات `school_platform`
2. اضغط على تبويب **"استيراد"** أو **"Import"**
3. اضغط **"اختيار ملف"** أو **"Choose File"**
4. اختر الملف: `C:\xampp\htdocs\school-platform\db\database.sql`
5. اضغط **"تنفيذ"** أو **"Go"**

### 6️⃣ اختبار الموقع

1. افتح المتصفح
2. اذهب إلى: `http://localhost/school-platform`
3. يجب أن تظهر الصفحة الرئيسية للموقع

## 🔑 بيانات الدخول الافتراضية

### حساب المدير
- **الرابط**: `http://localhost/school-platform/login.php`
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `password`

## ✅ التحقق من التثبيت

### اختبار الوظائف الأساسية:

1. **الصفحة الرئيسية** ✓
   - عرض المستويات التعليمية
   - عرض أحدث المحتويات
   - عمل شريط البحث

2. **نظام المستخدمين** ✓
   - تسجيل الدخول
   - إنشاء حساب جديد
   - تسجيل الخروج

3. **تصفح المحتوى** ✓
   - عرض المستويات
   - عرض المواد
   - عرض الملفات

4. **لوحة التحكم** ✓
   - دخول المدير
   - عرض الإحصائيات
   - إضافة ملف جديد

## 🔧 حل المشاكل الشائعة

### مشكلة: "لا يمكن الاتصال بقاعدة البيانات"
**الحل:**
1. تأكد من تشغيل MySQL في XAMPP
2. تحقق من إعدادات `db/config.php`
3. تأكد من إنشاء قاعدة البيانات `school_platform`

### مشكلة: "الصفحة لا تظهر"
**الحل:**
1. تأكد من تشغيل Apache في XAMPP
2. تحقق من المسار: `http://localhost/school-platform`
3. تأكد من نسخ الملفات في المكان الصحيح

### مشكلة: "خطأ في رفع الملفات"
**الحل:**
1. تأكد من وجود مجلد `uploads/`
2. تحقق من صلاحيات المجلد
3. تأكد من أن حجم الملف أقل من 10MB

### مشكلة: "التصميم لا يظهر بشكل صحيح"
**الحل:**
1. تحقق من تحميل ملفات CSS
2. تأكد من اتصال الإنترنت (لـ Font Awesome)
3. امسح cache المتصفح

## 📱 اختبار التجاوب

اختبر الموقع على:
- **سطح المكتب**: Chrome, Firefox, Edge
- **الجوال**: Safari, Chrome Mobile
- **التابلت**: iPad, Android Tablet

## 🔒 إعدادات الأمان (اختيارية)

### تغيير كلمة مرور المدير:
1. ادخل لوحة التحكم
2. اذهب إلى إعدادات الحساب
3. غير كلمة المرور

### تفعيل HTTPS (للإنتاج):
```apache
# في ملف .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## 📊 إضافة بيانات تجريبية

### إضافة مستخدمين تجريبيين:
```sql
INSERT INTO users (username, email, password, full_name, role) VALUES
('teacher1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أستاذ محمد', 'teacher'),
('student1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'الطالب أحمد', 'student');
```

### إضافة ملفات تجريبية:
1. ادخل كمدير
2. اذهب إلى "إضافة ملف جديد"
3. ارفع بعض الملفات التجريبية

## 🎯 الخطوات التالية

بعد التثبيت الناجح:

1. **تخصيص المحتوى**:
   - إضافة مواد دراسية جديدة
   - رفع الملفات التعليمية
   - تنظيم المحتوى

2. **إدارة المستخدمين**:
   - إنشاء حسابات للمعلمين
   - تعيين الصلاحيات
   - مراقبة الأنشطة

3. **تخصيص التصميم**:
   - تغيير الألوان والشعار
   - إضافة معلومات المؤسسة
   - تخصيص النصوص

4. **النسخ الاحتياطي**:
   - إعداد نسخ احتياطية دورية
   - حفظ قاعدة البيانات
   - نسخ الملفات المرفوعة

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. **راجع ملف README.md** للتفاصيل الكاملة
2. **تحقق من سجلات الأخطاء** في XAMPP
3. **ابحث في المشاكل الشائعة** أعلاه
4. **اتصل بالدعم الفني** إذا لزم الأمر

---

**🎉 مبروك! تم تثبيت منصة التعليم بنجاح**

الآن يمكنك البدء في استخدام المنصة وإضافة المحتوى التعليمي.
