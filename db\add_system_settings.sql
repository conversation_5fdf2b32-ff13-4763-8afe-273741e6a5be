-- إضافة جدول إعدادات النظام
-- تاريخ الإنشاء: 2024-12-19

-- إنشاء جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (setting_key),
    INDEX idx_public (is_public)
);

-- إدراج الإعدادات الافتراضية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'منصة التعليم', 'string', 'اسم الموقع', TRUE),
('site_description', 'منصة تعليمية شاملة للمراحل الدراسية والشهادات الرسمية', 'string', 'وصف الموقع', TRUE),
('site_keywords', 'تعليم,مدرسة,شهادات,امتحانات,دروس', 'string', 'كلمات مفتاحية للموقع', TRUE),
('admin_email', '<EMAIL>', 'string', 'بريد المدير الإلكتروني', FALSE),
('contact_email', '<EMAIL>', 'string', 'بريد التواصل', TRUE),
('contact_phone', '', 'string', 'رقم الهاتف للتواصل', TRUE),
('contact_address', '', 'string', 'عنوان المؤسسة', TRUE),
('max_file_size', '10485760', 'number', 'الحد الأقصى لحجم الملف بالبايت (10MB)', FALSE),
('allowed_file_types', 'pdf,doc,docx,ppt,pptx,xls,xlsx,jpg,jpeg,png,gif', 'string', 'أنواع الملفات المسموحة', FALSE),
('enable_registration', 'true', 'boolean', 'تفعيل التسجيل الجديد', FALSE),
('enable_comments', 'true', 'boolean', 'تفعيل التعليقات', FALSE),
('enable_ratings', 'true', 'boolean', 'تفعيل التقييمات', FALSE),
('items_per_page', '12', 'number', 'عدد العناصر في الصفحة الواحدة', FALSE),
('maintenance_mode', 'false', 'boolean', 'وضع الصيانة', FALSE),
('google_analytics_id', '', 'string', 'معرف Google Analytics', FALSE),
('facebook_url', '', 'string', 'رابط صفحة Facebook', TRUE),
('twitter_url', '', 'string', 'رابط صفحة Twitter', TRUE),
('youtube_url', '', 'string', 'رابط قناة YouTube', TRUE)
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;

-- إنشاء جدول سجل النشاطات إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT DEFAULT NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) DEFAULT NULL,
    record_id INT DEFAULT NULL,
    old_values JSON DEFAULT NULL,
    new_values JSON DEFAULT NULL,
    ip_address VARCHAR(45) DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_table_record (table_name, record_id),
    INDEX idx_created_at (created_at)
);

-- إضافة جدول الإشعارات إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_read (user_id, is_read),
    INDEX idx_created_at (created_at)
);

-- إضافة جدول المفضلة إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    document_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_document (user_id, document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_document_id (document_id)
);

-- إضافة جدول التقييمات إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    rating TINYINT CHECK (rating >= 1 AND rating <= 5),
    review TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_document (user_id, document_id),
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating)
);

-- إضافة جدول التعليقات إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT NOT NULL,
    parent_id INT DEFAULT NULL,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comments(id) ON DELETE CASCADE,
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_approved (is_approved)
);

-- إضافة جدول سجل التحميلات إذا لم يكن موجود
CREATE TABLE IF NOT EXISTS download_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_id INT NOT NULL,
    user_id INT DEFAULT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT DEFAULT NULL,
    download_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_document_id (document_id),
    INDEX idx_user_id (user_id),
    INDEX idx_download_date (download_date),
    INDEX idx_ip_address (ip_address)
);

-- التحقق من النتائج
SELECT 'تم إنشاء الجداول بنجاح!' as message;

-- عرض الإعدادات المضافة
SELECT 'الإعدادات المضافة:' as info, COUNT(*) as settings_count FROM system_settings;

-- عرض عينة من الإعدادات
SELECT setting_key, setting_value, setting_type FROM system_settings LIMIT 10;
