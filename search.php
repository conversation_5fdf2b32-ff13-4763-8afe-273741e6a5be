<?php
session_start();
require_once 'db/config.php';

// معالجة البحث
$query = trim($_GET['q'] ?? '');
$levelId = $_GET['level'] ?? '';
$subjectId = $_GET['subject'] ?? '';
$type = $_GET['type'] ?? '';
$examYear = $_GET['exam_year'] ?? '';
$examSession = $_GET['exam_session'] ?? '';
$sort = $_GET['sort'] ?? 'relevance';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 12;
$offset = ($page - 1) * $limit;

// التحقق من نوع البحث (عادي أم شهادات)
$isExamSearch = $_GET['type'] === 'exam' || $examYear || $examSession || ($levelId && isExamLevel($levelId));

$documents = [];
$totalDocuments = 0;
$totalPages = 0;

// إجراء البحث إذا كان هناك استعلام
if ($query || $levelId || $subjectId || $type) {
    $documents = searchDocuments($query, $levelId, $subjectId, $type);
    
    // ترتيب النتائج
    if ($sort === 'newest') {
        usort($documents, fn($a, $b) => strtotime($b['upload_date']) - strtotime($a['upload_date']));
    } elseif ($sort === 'oldest') {
        usort($documents, fn($a, $b) => strtotime($a['upload_date']) - strtotime($b['upload_date']));
    } elseif ($sort === 'title') {
        usort($documents, fn($a, $b) => strcmp($a['title'], $b['title']));
    } elseif ($sort === 'downloads') {
        usort($documents, fn($a, $b) => $b['download_count'] - $a['download_count']);
    }
    
    $totalDocuments = count($documents);
    $totalPages = ceil($totalDocuments / $limit);
    $documents = array_slice($documents, $offset, $limit);
}

// جلب البيانات للفلاتر
$levels = getLevels();
$subjects = [];
if ($levelId) {
    $subjects = getSubjectsByLevel($levelId);
}

// إحصائيات البحث
$searchStats = [];
if ($query) {
    $searchStats = [
        'total' => $totalDocuments,
        'lessons' => count(array_filter($documents, fn($d) => $d['type'] === 'درس')),
        'exams' => count(array_filter($documents, fn($d) => $d['type'] === 'امتحان')),
        'solutions' => count(array_filter($documents, fn($d) => $d['type'] === 'حل')),
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث<?= $query ? ' - ' . htmlspecialchars($query) : '' ?> - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .search-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }
        
        .search-header h1 {
            font-size: 2.5rem;
            margin-bottom: 2rem;
        }
        
        .advanced-search {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .search-form {
            display: grid;
            gap: 1.5rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: bold;
        }
        
        .form-input, .form-select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
        }
        
        .results-section {
            padding: 3rem 0;
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }
        
        .results-title {
            font-size: 2rem;
            color: #2c3e50;
        }
        
        .results-meta {
            color: #666;
            font-size: 0.9rem;
        }
        
        .search-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: white;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
        
        .sort-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .sort-select {
            padding: 0.7rem 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            background: white;
        }
        
        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }
        
        .no-results i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #ddd;
        }
        
        .search-suggestions {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .suggestions-title {
            color: #2c3e50;
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }
        
        .suggestions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .suggestion-tag {
            background: white;
            color: #3498db;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-decoration: none;
            font-size: 0.9rem;
            border: 2px solid #3498db;
            transition: all 0.3s ease;
        }
        
        .suggestion-tag:hover {
            background: #3498db;
            color: white;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
        }
        
        @media (max-width: 768px) {
            .search-header h1 {
                font-size: 2rem;
            }
            
            .advanced-search {
                margin: 0 1rem;
                padding: 1.5rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .results-header {
                flex-direction: column;
                text-align: center;
            }
            
            .sort-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li class="dropdown">
                        <a href="#"><i class="fas fa-layer-group"></i> المستويات <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <?php foreach($levels as $level): ?>
                                <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                    <li><a href="search.php" class="active"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isLoggedIn()): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if(isAdmin()): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Search Header -->
    <section class="search-header">
        <div class="container">
            <h1><i class="fas fa-search"></i> البحث المتقدم</h1>
            
            <div class="advanced-search">
                <form method="GET" action="" class="search-form">
                    <div class="form-group">
                        <label for="q">البحث عن:</label>
                        <input type="text" id="q" name="q" class="form-input" 
                               value="<?= htmlspecialchars($query) ?>" 
                               placeholder="ابحث عن درس، امتحان، أو موضوع...">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="level">المستوى:</label>
                            <select id="level" name="level" class="form-select" onchange="loadSubjects(this.value)">
                                <option value="">جميع المستويات</option>
                                <?php foreach($levels as $level): ?>
                                    <option value="<?= $level['id'] ?>" <?= $levelId == $level['id'] ? 'selected' : '' ?>>
                                        <?= $level['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">المادة:</label>
                            <select id="subject" name="subject" class="form-select">
                                <option value="">جميع المواد</option>
                                <?php foreach($subjects as $subject): ?>
                                    <option value="<?= $subject['id'] ?>" <?= $subjectId == $subject['id'] ? 'selected' : '' ?>>
                                        <?= $subject['name'] ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="type">نوع المحتوى:</label>
                            <select id="type" name="type" class="form-select">
                                <option value="">جميع الأنواع</option>
                                <option value="درس" <?= $type === 'درس' ? 'selected' : '' ?>>دروس</option>
                                <option value="امتحان" <?= $type === 'امتحان' ? 'selected' : '' ?>>امتحانات</option>
                                <option value="حل" <?= $type === 'حل' ? 'selected' : '' ?>>حلول نموذجية</option>
                                <option value="تمرين" <?= $type === 'تمرين' ? 'selected' : '' ?>>تمارين</option>
                                <option value="ملخص" <?= $type === 'ملخص' ? 'selected' : '' ?>>ملخصات</option>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Results Section -->
    <section class="results-section">
        <div class="container">
            <?php if ($query || $levelId || $subjectId || $type): ?>
                <div class="results-header">
                    <h2 class="results-title">نتائج البحث</h2>
                    <div class="results-meta">
                        <?php if ($query): ?>
                            البحث عن: "<strong><?= htmlspecialchars($query) ?></strong>"<br>
                        <?php endif; ?>
                        تم العثور على <?= $totalDocuments ?> نتيجة
                    </div>
                </div>

                <?php if ($searchStats && $totalDocuments > 0): ?>
                    <div class="search-stats">
                        <div class="stat-item">
                            <div class="stat-number"><?= $searchStats['total'] ?></div>
                            <div class="stat-label">إجمالي النتائج</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?= $searchStats['lessons'] ?></div>
                            <div class="stat-label">درس</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?= $searchStats['exams'] ?></div>
                            <div class="stat-label">امتحان</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number"><?= $searchStats['solutions'] ?></div>
                            <div class="stat-label">حل نموذجي</div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($documents): ?>
                    <div class="sort-controls">
                        <label for="sort">ترتيب حسب:</label>
                        <select id="sort" class="sort-select" onchange="changeSortOrder(this.value)">
                            <option value="relevance" <?= $sort === 'relevance' ? 'selected' : '' ?>>الأكثر صلة</option>
                            <option value="newest" <?= $sort === 'newest' ? 'selected' : '' ?>>الأحدث</option>
                            <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>الأقدم</option>
                            <option value="title" <?= $sort === 'title' ? 'selected' : '' ?>>الاسم</option>
                            <option value="downloads" <?= $sort === 'downloads' ? 'selected' : '' ?>>الأكثر تحميلاً</option>
                        </select>
                    </div>

                    <div class="content-grid">
                        <?php foreach($documents as $doc): ?>
                            <div class="content-card">
                                <div class="content-type">
                                    <i class="fas fa-<?= $doc['type'] == 'درس' ? 'book' : ($doc['type'] == 'امتحان' ? 'file-alt' : ($doc['type'] == 'حل' ? 'check-circle' : ($doc['type'] == 'تمرين' ? 'pencil-alt' : 'clipboard-list'))) ?>"></i>
                                    <span><?= $doc['type'] ?></span>
                                </div>
                                <h4><?= highlightSearchTerm(htmlspecialchars($doc['title']), $query) ?></h4>
                                <p class="content-meta">
                                    <span><i class="fas fa-layer-group"></i> <?= $doc['level_name'] ?></span>
                                    <span><i class="fas fa-book"></i> <?= $doc['subject_name'] ?></span>
                                </p>
                                <?php if($doc['description']): ?>
                                    <p class="content-description">
                                        <?= highlightSearchTerm(htmlspecialchars(substr($doc['description'], 0, 100)), $query) ?>...
                                    </p>
                                <?php endif; ?>
                                <div class="content-actions">
                                    <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline">عرض</a>
                                    <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-primary">تحميل</a>
                                </div>
                                <div class="content-date">
                                    <i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($doc['upload_date'])) ?>
                                    <i class="fas fa-download"></i> <?= $doc['download_count'] ?> تحميل
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if($page > 1): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                    <i class="fas fa-chevron-right"></i> السابق
                                </a>
                            <?php endif; ?>
                            
                            <?php for($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <?php if($i == $page): ?>
                                    <span class="current"><?= $i ?></span>
                                <?php else: ?>
                                    <a href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>"><?= $i ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>
                            
                            <?php if($page < $totalPages): ?>
                                <a href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                    التالي <i class="fas fa-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="no-results">
                        <i class="fas fa-search"></i>
                        <h3>لم يتم العثور على نتائج</h3>
                        <p>لم نتمكن من العثور على أي محتوى يطابق بحثك</p>
                        
                        <div class="search-suggestions">
                            <h4 class="suggestions-title">جرب البحث عن:</h4>
                            <div class="suggestions-list">
                                <a href="?q=رياضيات" class="suggestion-tag">رياضيات</a>
                                <a href="?q=فيزياء" class="suggestion-tag">فيزياء</a>
                                <a href="?q=كيمياء" class="suggestion-tag">كيمياء</a>
                                <a href="?q=لغة عربية" class="suggestion-tag">لغة عربية</a>
                                <a href="?q=تاريخ" class="suggestion-tag">تاريخ</a>
                                <a href="?q=جغرافيا" class="suggestion-tag">جغرافيا</a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <h3>ابدأ البحث</h3>
                    <p>استخدم النموذج أعلاه للبحث عن الدروس والامتحانات والحلول</p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>المستويات</h4>
                    <ul>
                        <?php foreach($levels as $level): ?>
                            <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
    <script>
        function loadSubjects(levelId) {
            const subjectSelect = document.getElementById('subject');
            subjectSelect.innerHTML = '<option value="">جميع المواد</option>';
            
            if (levelId) {
                fetch(`ajax/get_subjects.php?level_id=${levelId}`)
                    .then(response => response.json())
                    .then(subjects => {
                        subjects.forEach(subject => {
                            const option = document.createElement('option');
                            option.value = subject.id;
                            option.textContent = subject.name;
                            subjectSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error loading subjects:', error));
            }
        }
        
        function changeSortOrder(sort) {
            const url = new URL(window.location);
            url.searchParams.set('sort', sort);
            url.searchParams.set('page', '1');
            window.location.href = url.toString();
        }
    </script>
</body>
</html>

<?php
// دالة لتمييز كلمات البحث
function highlightSearchTerm($text, $term) {
    if (!$term) return $text;
    return preg_replace('/(' . preg_quote($term, '/') . ')/ui', '<span class="highlight">$1</span>', $text);
}
?>
