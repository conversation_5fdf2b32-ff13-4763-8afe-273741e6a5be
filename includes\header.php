<?php
// جلب جميع المستويات للقائمة
if (!isset($allLevels)) {
    $allLevels = getLevels();
}
?>

<header class="header">
    <div class="container">
        <div class="nav-brand">
            <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
        </div>
        <nav class="nav-menu">
            <ul>
                <li><a href="index.php" class="<?= basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : '' ?>">
                    <i class="fas fa-home"></i> الرئيسية
                </a></li>
                
                <li class="dropdown">
                    <a href="#"><i class="fas fa-layer-group"></i> المستويات <i class="fas fa-chevron-down"></i></a>
                    <ul class="dropdown-menu">
                        <?php foreach($allLevels as $level): ?>
                            <?php 
                            $isExamLevel = isExamLevel($level['id']);
                            $linkUrl = $isExamLevel ? "exam_level.php?id={$level['id']}" : "level.php?id={$level['id']}";
                            $iconClass = $isExamLevel ? 'fas fa-certificate' : 'fas fa-book';
                            ?>
                            <li>
                                <a href="<?= $linkUrl ?>">
                                    <i class="<?= $iconClass ?>"></i> <?= $level['name'] ?>
                                    <?php if($isExamLevel): ?>
                                        <span class="exam-indicator">شهادة</span>
                                    <?php endif; ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </li>
                
                <li><a href="search.php" class="<?= basename($_SERVER['PHP_SELF']) == 'search.php' ? 'active' : '' ?>">
                    <i class="fas fa-search"></i> البحث
                </a></li>
                
                <?php if(isset($_SESSION['user_id'])): ?>
                    <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                    <?php if($_SESSION['role'] == 'admin'): ?>
                        <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                    <?php endif; ?>
                    <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                <?php else: ?>
                    <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                    <li><a href="register.php"><i class="fas fa-user-plus"></i> تسجيل جديد</a></li>
                <?php endif; ?>
            </ul>
        </nav>
        <div class="mobile-menu-toggle">
            <i class="fas fa-bars"></i>
        </div>
    </div>
</header>

<style>
.exam-indicator {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dropdown-menu a i {
    margin-left: 0.5rem;
    width: 16px;
}
</style>
