<?php
session_start();
require_once 'db/config.php';

// جلب أحدث الدروس والامتحانات
$latest_documents = $pdo->query("
    SELECT d.*, s.name as subject_name, l.name as level_name 
    FROM documents d 
    JOIN subjects s ON d.subject_id = s.id 
    JOIN levels l ON s.level_id = l.id 
    ORDER BY d.upload_date DESC 
    LIMIT 6
")->fetchAll();

// جلب المستويات التعليمية
$levels = $pdo->query("SELECT * FROM levels ORDER BY id")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة دروس وامتحانات مدرسية</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php" class="active"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li class="dropdown">
                        <a href="#"><i class="fas fa-layer-group"></i> المستويات <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <?php foreach($levels as $level): ?>
                                <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                    <li><a href="search.php"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isset($_SESSION['user_id'])): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if($_SESSION['role'] == 'admin'): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="particles-container" id="particles"></div>
        <div class="container">
            <div class="hero-content">
                <h2 class="typewriter fade-in">مرحباً بك في منصة التعليم الشاملة</h2>
                <p class="fade-in" style="animation-delay: 0.5s">دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                <div class="search-box">
                    <form action="search.php" method="GET">
                        <div class="search-input-group">
                            <input type="text" name="q" placeholder="ابحث عن درس أو امتحان...">
                            <select name="level">
                                <option value="">جميع المستويات</option>
                                <?php foreach($levels as $level): ?>
                                    <option value="<?= $level['id'] ?>"><?= $level['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Levels Section -->
    <section class="levels-section">
        <div class="container">
            <h3 class="slide-in-top"><i class="fas fa-layer-group"></i> المستويات التعليمية</h3>
            <div class="levels-grid">
                <?php foreach($levels as $index => $level): ?>
                    <?php
                    $isExamLevel = isExamLevel($level['id']);
                    $linkUrl = $isExamLevel ? "exam_level.php?id={$level['id']}" : "level.php?id={$level['id']}";
                    $iconClass = $isExamLevel ? 'fas fa-certificate' : 'fas fa-book-open';
                    $buttonText = $isExamLevel ? 'استكشف الشهادة' : 'استكشف المواد';
                    ?>
                    <div class="level-card zoom-in interactive-element gpu-accelerated wave-effect light-beam <?= $isExamLevel ? 'exam-level-card' : '' ?>"
                         data-level-id="<?= $level['id'] ?>"
                         style="animation-delay: <?= $index * 0.2 ?>s">
                        <div class="level-icon">
                            <i class="<?= $iconClass ?>"></i>
                            <?php if($isExamLevel): ?>
                                <div class="exam-badge">شهادة رسمية</div>
                            <?php endif; ?>
                        </div>
                        <h4><?= $level['name'] ?></h4>
                        <p><?= $level['description'] ?? 'جميع المواد الدراسية متوفرة' ?></p>
                        <a href="<?= $linkUrl ?>" class="btn btn-primary ripple-effect explosion-effect"><?= $buttonText ?></a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Latest Content Section -->
    <section class="latest-content">
        <div class="container">
            <h3 class="slide-in-top"><i class="fas fa-clock"></i> أحدث المحتويات</h3>
            <div class="content-grid">
                <?php foreach($latest_documents as $index => $doc): ?>
                    <div class="content-card slide-in-bottom interactive-element gpu-accelerated ripple-effect"
                         style="animation-delay: <?= $index * 0.15 ?>s">
                        <div class="content-type">
                            <i class="fas fa-<?= $doc['type'] == 'درس' ? 'book' : ($doc['type'] == 'امتحان' ? 'file-alt' : 'check-circle') ?>"></i>
                            <span><?= $doc['type'] ?></span>
                        </div>
                        <h4><?= $doc['title'] ?></h4>
                        <p class="content-meta">
                            <span><i class="fas fa-layer-group"></i> <?= $doc['level_name'] ?></span>
                            <span><i class="fas fa-book"></i> <?= $doc['subject_name'] ?></span>
                        </p>
                        <p class="content-description"><?= substr($doc['description'], 0, 100) ?>...</p>
                        <div class="content-actions">
                            <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline ripple-effect wave-effect">عرض</a>
                            <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-primary ripple-effect explosion-effect">تحميل</a>
                        </div>
                        <div class="content-date">
                            <i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($doc['upload_date'])) ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.1s">
                    <div class="stat-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'درس'")->fetchColumn() ?></h4>
                        <p>درس متاح</p>
                    </div>
                </div>
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.3s">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'امتحان'")->fetchColumn() ?></h4>
                        <p>امتحان وتمرين</p>
                    </div>
                </div>
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.5s">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'حل'")->fetchColumn() ?></h4>
                        <p>حل نموذجي</p>
                    </div>
                </div>
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.7s">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn() ?></h4>
                        <p>مستخدم مسجل</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>المستويات</h4>
                    <ul>
                        <?php foreach($levels as $level): ?>
                            <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
    <script src="assets/js/enhancements.js"></script>
</body>
</html>
