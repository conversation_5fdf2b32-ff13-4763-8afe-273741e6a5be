<?php
session_start();
require_once 'db/config.php';

// جلب أحدث الدروس والامتحانات
$latest_documents = $pdo->query("
    SELECT d.*, s.name as subject_name, l.name as level_name 
    FROM documents d 
    JOIN subjects s ON d.subject_id = s.id 
    JOIN levels l ON s.level_id = l.id 
    ORDER BY d.upload_date DESC 
    LIMIT 6
")->fetchAll();

// جلب المستويات التعليمية
$levels = $pdo->query("SELECT * FROM levels ORDER BY id")->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة دروس وامتحانات مدرسية</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php" class="active"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li class="dropdown">
                        <a href="#"><i class="fas fa-layer-group"></i> المستويات <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <?php foreach($levels as $level): ?>
                                <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                    <li><a href="search.php"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isset($_SESSION['user_id'])): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if($_SESSION['role'] == 'admin'): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="particles-container" id="particles"></div>
        <div class="container">
            <div class="hero-content">
                <h2 class="typewriter fade-in">مرحباً بك في منصة التعليم الشاملة</h2>
                <p class="fade-in" style="animation-delay: 0.5s">دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                <div class="search-box">
                    <form action="search.php" method="GET">
                        <div class="search-input-group">
                            <input type="text" name="q" placeholder="ابحث عن درس أو امتحان...">
                            <select name="level">
                                <option value="">جميع المستويات</option>
                                <?php foreach($levels as $level): ?>
                                    <option value="<?= $level['id'] ?>"><?= $level['name'] ?></option>
                                <?php endforeach; ?>
                            </select>
                            <button type="submit"><i class="fas fa-search"></i></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Levels Section -->
    <section class="levels-section">
        <div class="container">
            <h3 class="slide-in-top"><i class="fas fa-layer-group"></i> المستويات التعليمية</h3>
            <div class="levels-grid">
                <?php foreach($levels as $index => $level): ?>
                    <?php
                    // عرض المستويات التعليمية العادية فقط (ليس الشهادات)
                    if(isExamLevel($level['id'])) continue;
                    ?>
                    <div class="level-card zoom-in interactive-element gpu-accelerated wave-effect light-beam"
                         data-level-id="<?= $level['id'] ?>"
                         style="animation-delay: <?= $index * 0.2 ?>s">
                        <div class="level-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h4><?= $level['name'] ?></h4>
                        <p><?= $level['description'] ?? 'جميع المواد الدراسية متوفرة' ?></p>
                        <a href="level.php?id=<?= $level['id'] ?>" class="btn btn-primary ripple-effect explosion-effect">استكشف المواد</a>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Exam Certificates Section -->
    <section class="exam-certificates-section">
        <div class="container">
            <h3 class="slide-in-top"><i class="fas fa-certificate"></i> الشهادات الرسمية</h3>
            <p class="section-description fade-in" style="animation-delay: 0.3s">
                مواضيع وحلول امتحانات الشهادات الرسمية لجميع السنوات والمواد
            </p>
            <div class="exam-certificates-grid">
                <?php
                // جلب الشهادات الرسمية فقط
                $examLevels = [];
                foreach($levels as $level) {
                    if(isExamLevel($level['id'])) {
                        $examLevels[] = $level;
                    }
                }
                ?>

                <?php foreach($examLevels as $index => $examLevel): ?>
                    <?php
                    // جلب إحصائيات الشهادة
                    $examStats = getExamStatistics($examLevel['id']);
                    $examYears = getExamYears($examLevel['id']);
                    $latestExamDocs = getLatestExamDocuments($examLevel['id'], 3);

                    // تحديد معلومات الشهادة
                    $examInfo = [
                        4 => [
                            'name' => 'شهادة التعليم المتوسط',
                            'short_name' => 'BEM',
                            'description' => 'مواضيع وحلول امتحانات شهادة التعليم المتوسط من 2015 إلى 2024',
                            'color' => '#27ae60',
                            'gradient' => 'linear-gradient(135deg, #27ae60, #2ecc71)',
                            'icon' => 'fas fa-graduation-cap',
                            'subjects_count' => 8,
                            'years_range' => '2015-2024'
                        ],
                        5 => [
                            'name' => 'شهادة البكالوريا',
                            'short_name' => 'BAC',
                            'description' => 'مواضيع وحلول امتحانات شهادة البكالوريا لجميع الشعب من 2010 إلى 2024',
                            'color' => '#e74c3c',
                            'gradient' => 'linear-gradient(135deg, #e74c3c, #c0392b)',
                            'icon' => 'fas fa-university',
                            'subjects_count' => 20,
                            'years_range' => '2010-2024'
                        ]
                    ];

                    $info = $examInfo[$examLevel['id']];
                    ?>

                    <div class="exam-certificate-card bounce-in interactive-element gpu-accelerated"
                         style="--exam-color: <?= $info['color'] ?>; --exam-gradient: <?= $info['gradient'] ?>; animation-delay: <?= $index * 0.3 ?>s"
                         onclick="window.location.href='exam_level.php?id=<?= $examLevel['id'] ?>'">

                        <!-- Header -->
                        <div class="exam-card-header">
                            <div class="exam-badge-official">شهادة رسمية</div>
                            <div class="exam-icon">
                                <i class="<?= $info['icon'] ?>"></i>
                            </div>
                            <div class="exam-short-name"><?= $info['short_name'] ?></div>
                        </div>

                        <!-- Content -->
                        <div class="exam-card-content">
                            <h3><?= $info['name'] ?></h3>
                            <p class="exam-description"><?= $info['description'] ?></p>

                            <!-- Stats -->
                            <div class="exam-stats">
                                <div class="exam-stat">
                                    <i class="fas fa-book"></i>
                                    <span><?= $info['subjects_count'] ?></span>
                                    <small>مادة</small>
                                </div>
                                <div class="exam-stat">
                                    <i class="fas fa-calendar"></i>
                                    <span><?= count($examYears) ?></span>
                                    <small>سنة</small>
                                </div>
                                <div class="exam-stat">
                                    <i class="fas fa-file-alt"></i>
                                    <span><?= $examStats['total_subjects'] ?? 0 ?></span>
                                    <small>موضوع</small>
                                </div>
                                <div class="exam-stat">
                                    <i class="fas fa-check-circle"></i>
                                    <span><?= $examStats['total_solutions'] ?? 0 ?></span>
                                    <small>حل</small>
                                </div>
                            </div>

                            <!-- Years Range -->
                            <div class="exam-years-range">
                                <i class="fas fa-history"></i>
                                <span>السنوات المتاحة: <?= $info['years_range'] ?></span>
                            </div>

                            <!-- Latest Documents Preview -->
                            <?php if($latestExamDocs): ?>
                            <div class="exam-latest-preview">
                                <h5><i class="fas fa-clock"></i> أحدث المواضيع</h5>
                                <div class="latest-docs-list">
                                    <?php foreach(array_slice($latestExamDocs, 0, 3) as $doc): ?>
                                        <div class="latest-doc-item">
                                            <i class="fas fa-<?= $doc['type'] == 'موضوع شهادة' ? 'file-alt' : 'check-circle' ?>"></i>
                                            <span><?= substr($doc['title'], 0, 30) ?>...</span>
                                            <small><?= $doc['exam_year'] ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Footer -->
                        <div class="exam-card-footer">
                            <a href="exam_level.php?id=<?= $examLevel['id'] ?>" class="btn btn-exam ripple-effect explosion-effect">
                                <i class="fas fa-arrow-left"></i>
                                استكشف الشهادة
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Exam Documents Quick Access Section -->
    <section class="exam-documents-section">
        <div class="container">
            <h3 class="slide-in-top"><i class="fas fa-file-alt"></i> مواضيع وحلول الشهادات الرسمية</h3>
            <p class="section-description fade-in" style="animation-delay: 0.3s">
                الوصول المباشر لجميع مواضيع وحلول امتحانات الشهادات الرسمية مرتبة حسب السنة والمادة
            </p>

            <div class="exam-documents-grid">
                <?php
                // جلب جميع مواضيع وحلول الشهادات
                $examDocumentsStmt = $pdo->prepare("
                    SELECT d.*, s.name as subject_name, s.color as subject_color, s.icon as subject_icon,
                           l.name as level_name, l.id as level_id
                    FROM documents d
                    JOIN subjects s ON d.subject_id = s.id
                    JOIN levels l ON s.level_id = l.id
                    WHERE l.id IN (4, 5) AND d.type IN ('موضوع شهادة', 'حل شهادة')
                    ORDER BY d.exam_year DESC, l.id ASC, s.name ASC, d.type ASC
                    LIMIT 12
                ");
                $examDocumentsStmt->execute();
                $examDocuments = $examDocumentsStmt->fetchAll();
                ?>

                <?php foreach($examDocuments as $index => $doc): ?>
                    <div class="exam-document-card slide-in-bottom interactive-element gpu-accelerated"
                         style="--subject-color: <?= $doc['subject_color'] ?>; animation-delay: <?= $index * 0.1 ?>s">

                        <!-- Document Header -->
                        <div class="exam-doc-header">
                            <div class="exam-doc-type">
                                <i class="fas fa-<?= $doc['type'] == 'موضوع شهادة' ? 'file-alt' : 'check-circle' ?>"></i>
                                <span><?= $doc['type'] ?></span>
                            </div>
                            <div class="exam-doc-year"><?= $doc['exam_year'] ?></div>
                        </div>

                        <!-- Document Content -->
                        <div class="exam-doc-content">
                            <div class="exam-doc-level">
                                <i class="fas fa-certificate"></i>
                                <span><?= $doc['level_id'] == 4 ? 'BEM' : 'BAC' ?></span>
                            </div>

                            <div class="exam-doc-subject">
                                <i class="<?= $doc['subject_icon'] ?>"></i>
                                <span><?= $doc['subject_name'] ?></span>
                            </div>

                            <h4 class="exam-doc-title"><?= $doc['title'] ?></h4>

                            <?php if($doc['exam_session']): ?>
                                <div class="exam-doc-session">
                                    <i class="fas fa-calendar-check"></i>
                                    <span><?= $doc['exam_session'] ?></span>
                                </div>
                            <?php endif; ?>

                            <div class="exam-doc-meta">
                                <span><i class="fas fa-download"></i> <?= $doc['download_count'] ?></span>
                                <span><i class="fas fa-eye"></i> <?= $doc['view_count'] ?></span>
                                <span><i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($doc['upload_date'])) ?></span>
                            </div>
                        </div>

                        <!-- Document Actions -->
                        <div class="exam-doc-actions">
                            <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline-exam ripple-effect">
                                <i class="fas fa-eye"></i> عرض
                            </a>
                            <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-exam ripple-effect">
                                <i class="fas fa-download"></i> تحميل
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Quick Access Buttons -->
            <div class="exam-quick-access">
                <h4><i class="fas fa-rocket"></i> الوصول السريع</h4>
                <div class="quick-access-buttons">
                    <a href="exam_level.php?id=4" class="quick-access-btn bem-btn ripple-effect">
                        <i class="fas fa-graduation-cap"></i>
                        <span>شهادة التعليم المتوسط</span>
                        <small>جميع المواضيع والحلول</small>
                    </a>
                    <a href="exam_level.php?id=5" class="quick-access-btn bac-btn ripple-effect">
                        <i class="fas fa-university"></i>
                        <span>شهادة البكالوريا</span>
                        <small>جميع الشعب والمواد</small>
                    </a>
                </div>

                <!-- Filter by Year -->
                <div class="year-filter-section">
                    <h5><i class="fas fa-filter"></i> البحث حسب السنة</h5>
                    <div class="year-filter-buttons">
                        <?php
                        // جلب السنوات المتاحة
                        $yearsStmt = $pdo->prepare("
                            SELECT DISTINCT exam_year
                            FROM documents
                            WHERE exam_year IS NOT NULL AND type IN ('موضوع شهادة', 'حل شهادة')
                            ORDER BY exam_year DESC
                            LIMIT 8
                        ");
                        $yearsStmt->execute();
                        $availableYears = $yearsStmt->fetchAll(PDO::FETCH_COLUMN);
                        ?>

                        <?php foreach($availableYears as $year): ?>
                            <a href="search.php?exam_year=<?= $year ?>&type=exam" class="year-filter-btn ripple-effect">
                                <i class="fas fa-calendar-alt"></i>
                                <span><?= $year ?></span>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Latest Content Section -->
    <section class="latest-content">
        <div class="container">
            <h3 class="slide-in-top"><i class="fas fa-clock"></i> أحدث المحتويات</h3>
            <div class="content-grid">
                <?php foreach($latest_documents as $index => $doc): ?>
                    <div class="content-card slide-in-bottom interactive-element gpu-accelerated ripple-effect"
                         style="animation-delay: <?= $index * 0.15 ?>s">
                        <div class="content-type">
                            <i class="fas fa-<?= $doc['type'] == 'درس' ? 'book' : ($doc['type'] == 'امتحان' ? 'file-alt' : 'check-circle') ?>"></i>
                            <span><?= $doc['type'] ?></span>
                        </div>
                        <h4><?= $doc['title'] ?></h4>
                        <p class="content-meta">
                            <span><i class="fas fa-layer-group"></i> <?= $doc['level_name'] ?></span>
                            <span><i class="fas fa-book"></i> <?= $doc['subject_name'] ?></span>
                        </p>
                        <p class="content-description"><?= substr($doc['description'], 0, 100) ?>...</p>
                        <div class="content-actions">
                            <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline ripple-effect wave-effect">عرض</a>
                            <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-primary ripple-effect explosion-effect">تحميل</a>
                        </div>
                        <div class="content-date">
                            <i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($doc['upload_date'])) ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.1s">
                    <div class="stat-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'درس'")->fetchColumn() ?></h4>
                        <p>درس متاح</p>
                    </div>
                </div>
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.3s">
                    <div class="stat-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'امتحان'")->fetchColumn() ?></h4>
                        <p>امتحان وتمرين</p>
                    </div>
                </div>
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.5s">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM documents WHERE type = 'حل'")->fetchColumn() ?></h4>
                        <p>حل نموذجي</p>
                    </div>
                </div>
                <div class="stat-card bounce-in interactive-element gpu-accelerated light-beam" style="animation-delay: 0.7s">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h4 class="animate-number"><?= $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn() ?></h4>
                        <p>مستخدم مسجل</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>المستويات</h4>
                    <ul>
                        <?php foreach($levels as $level): ?>
                            <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
    <script src="assets/js/enhancements.js"></script>
</body>
</html>
