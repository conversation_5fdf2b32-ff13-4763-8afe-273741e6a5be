<?php
session_start();
require_once 'db/config.php';

// تعيين رمز الاستجابة 404
http_response_code(404);

// جلب المستويات للقائمة
$levels = getLevels();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة غير موجودة - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .error-container {
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        .error-content {
            max-width: 600px;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .error-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .error-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .error-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .suggestions {
            background: white;
            color: #333;
            padding: 3rem 0;
        }
        
        .suggestions-title {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #2c3e50;
        }
        
        .suggestions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .suggestion-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .suggestion-card:hover {
            transform: translateY(-5px);
            border-color: #3498db;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .suggestion-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 1rem;
        }
        
        .suggestion-card h3 {
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .suggestion-card p {
            color: #666;
            margin-bottom: 1.5rem;
        }
        
        .suggestion-card a {
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
        }
        
        .suggestion-card a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 5rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .error-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li class="dropdown">
                        <a href="#"><i class="fas fa-layer-group"></i> المستويات <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <?php foreach($levels as $level): ?>
                                <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                            <?php endforeach; ?>
                        </ul>
                    </li>
                    <li><a href="search.php"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isLoggedIn()): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if(isAdmin()): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Error Container -->
    <section class="error-container">
        <div class="error-content">
            <div class="error-code">404</div>
            <h1 class="error-title">الصفحة غير موجودة</h1>
            <p class="error-message">
                عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            </p>
            <div class="error-actions">
                <a href="index.php" class="error-btn">
                    <i class="fas fa-home"></i> العودة للرئيسية
                </a>
                <a href="search.php" class="error-btn">
                    <i class="fas fa-search"></i> البحث في الموقع
                </a>
                <a href="javascript:history.back()" class="error-btn">
                    <i class="fas fa-arrow-right"></i> العودة للخلف
                </a>
            </div>
        </div>
    </section>

    <!-- Suggestions -->
    <section class="suggestions">
        <div class="container">
            <h2 class="suggestions-title">ربما تبحث عن:</h2>
            <div class="suggestions-grid">
                <div class="suggestion-card">
                    <div class="suggestion-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <h3>المستويات التعليمية</h3>
                    <p>تصفح جميع المستويات التعليمية والمواد الدراسية المتاحة</p>
                    <a href="index.php#levels">استكشف المستويات</a>
                </div>
                
                <div class="suggestion-card">
                    <div class="suggestion-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>البحث المتقدم</h3>
                    <p>ابحث عن الدروس والامتحانات والحلول باستخدام البحث المتقدم</p>
                    <a href="search.php">بدء البحث</a>
                </div>
                
                <div class="suggestion-card">
                    <div class="suggestion-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3>أحدث المحتويات</h3>
                    <p>اطلع على آخر الدروس والامتحانات المضافة للمنصة</p>
                    <a href="index.php#latest">أحدث المحتويات</a>
                </div>
                
                <div class="suggestion-card">
                    <div class="suggestion-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h3>إنشاء حساب</h3>
                    <p>أنشئ حساب جديد للاستفادة من جميع ميزات المنصة</p>
                    <a href="register.php">إنشاء حساب</a>
                </div>
                
                <div class="suggestion-card">
                    <div class="suggestion-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <h3>المواد الدراسية</h3>
                    <p>تصفح المواد الدراسية المختلفة لجميع المستويات</p>
                    <a href="index.php">تصفح المواد</a>
                </div>
                
                <div class="suggestion-card">
                    <div class="suggestion-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3>المساعدة</h3>
                    <p>هل تحتاج مساعدة؟ تواصل معنا للحصول على الدعم</p>
                    <a href="contact.php">اتصل بنا</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>المستويات</h4>
                    <ul>
                        <?php foreach($levels as $level): ?>
                            <li><a href="level.php?id=<?= $level['id'] ?>"><?= $level['name'] ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>تواصل معنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
</body>
</html>
