# تحديث المستويات التعليمية 📚

## التغيير المطلوب
تحديث تقسيم المستويات التعليمية ليصبح:
- **الابتدائي**: الصفوف 1-5 (بدلاً من 1-6)
- **المتوسط**: الصفوف 6-9 (بدلاً من 7-9)  
- **الثانوي**: الصفوف 10-12 (بدون تغيير)

## الملفات المحدثة ✅

### 1. قاعدة البيانات
- ✅ `db/database.sql` - تحديث أوصاف المستويات في البيانات الأولية
- ✅ `db/update_levels.sql` - ملف تحديث منفصل لقواعد البيانات الموجودة

### 2. ملفات التوثيق
- ✅ `README.md` - تحديث قسم المستويات التعليمية
- ✅ `LAYOUT_IMPROVEMENTS.md` - تحديث المعلومات في ملف التحسينات
- ✅ الذكريات (Memories) - تحديث المعلومات المحفوظة

## التحديثات المطبقة

### قاعدة البيانات
```sql
-- المرحلة الابتدائية
UPDATE levels 
SET description = 'المرحلة الابتدائية من الصف الأول إلى الخامس'
WHERE name = 'الابتدائي';

-- المرحلة المتوسطة  
UPDATE levels 
SET description = 'المرحلة المتوسطة من الصف السادس إلى التاسع'
WHERE name = 'المتوسط';

-- المرحلة الثانوية
UPDATE levels 
SET description = 'المرحلة الثانوية من الصف العاشر إلى الثاني عشر'
WHERE name = 'الثانوي';
```

### ملف README.md
```markdown
### المستويات التعليمية
- **الابتدائي**: الصفوف 1-5 
- **المتوسط**: الصفوف 6-9   
- **الثانوي**: الصفوف 10-12
```

## الملفات التي لا تحتاج تحديث

### ملفات PHP الوظيفية
- `index.php` - يعرض المستويات من قاعدة البيانات ديناميكياً
- `level.php` - يعرض المواد حسب المستوى من قاعدة البيانات
- `subject.php` - يعرض الوثائق حسب المادة من قاعدة البيانات
- `search.php` - يستخدم البيانات من قاعدة البيانات
- `db/config.php` - دوال عامة لا تحتوي على أوصاف ثابتة

### السبب
هذه الملفات تستخدم البيانات من قاعدة البيانات ديناميكياً، لذلك ستعكس التغييرات تلقائياً بعد تحديث قاعدة البيانات.

## خطوات تطبيق التحديث

### للمشاريع الجديدة
1. استخدم ملف `db/database.sql` المحدث
2. سيتم إنشاء المستويات بالأوصاف الجديدة تلقائياً

### للمشاريع الموجودة
1. قم بتشغيل ملف `db/update_levels.sql` على قاعدة البيانات:
```bash
mysql -u username -p school_platform < db/update_levels.sql
```

2. أو قم بتنفيذ الاستعلامات يدوياً في phpMyAdmin:
```sql
UPDATE levels 
SET description = 'المرحلة الابتدائية من الصف الأول إلى الخامس'
WHERE name = 'الابتدائي';

UPDATE levels 
SET description = 'المرحلة المتوسطة من الصف السادس إلى التاسع'
WHERE name = 'المتوسط';
```

## التحقق من التحديث

### 1. فحص قاعدة البيانات
```sql
SELECT id, name, description FROM levels ORDER BY id;
```

### 2. فحص الموقع
- اذهب إلى الصفحة الرئيسية
- تحقق من أوصاف المستويات في البطاقات
- تأكد من عرض الأوصاف الجديدة

### 3. فحص صفحات المستويات
- ادخل إلى كل مستوى
- تحقق من عرض الوصف الصحيح
- تأكد من عمل جميع الوظائف بشكل طبيعي

## النتيجة المتوقعة

بعد تطبيق التحديث، ستظهر المستويات كالتالي:

### الابتدائي
- **الوصف**: "المرحلة الابتدائية من الصف الأول إلى الخامس"
- **المواد**: الرياضيات، اللغة العربية، العلوم، التربية الإسلامية، التاريخ، الجغرافيا

### المتوسط  
- **الوصف**: "المرحلة المتوسطة من الصف السادس إلى التاسع"
- **المواد**: الرياضيات، اللغة العربية، العلوم الفيزيائية، علوم الطبيعة والحياة، التاريخ والجغرافيا، التربية الإسلامية، اللغة الفرنسية، اللغة الإنجليزية

### الثانوي
- **الوصف**: "المرحلة الثانوية من الصف العاشر إلى الثاني عشر"  
- **المواد**: الرياضيات، الفيزياء، الكيمياء، علوم الطبيعة والحياة، اللغة العربية وآدابها، التاريخ والجغرافيا، الفلسفة، اللغة الفرنسية، اللغة الإنجليزية، التربية الإسلامية

## ملاحظات مهمة

1. **لا تأثير على البيانات الموجودة**: التحديث يؤثر فقط على أوصاف المستويات وليس على المواد أو الوثائق الموجودة

2. **التوافق مع النظام**: جميع الوظائف ستعمل بشكل طبيعي بعد التحديث

3. **النسخ الاحتياطي**: يُنصح بأخذ نسخة احتياطية من قاعدة البيانات قبل التحديث

4. **التحديث التلقائي**: جميع صفحات الموقع ستعكس التغييرات تلقائياً

---

**تم تطبيق جميع التحديثات بنجاح! ✅**

المستويات التعليمية الآن تتبع التقسيم الجديد: الابتدائي (1-5)، المتوسط (6-9)، الثانوي (10-12).
