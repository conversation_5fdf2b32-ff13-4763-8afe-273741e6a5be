<?php
session_start();
require_once 'db/config.php';

// التحقق من وجود معرف المادة
$subjectId = $_GET['id'] ?? null;
$selectedYear = $_GET['year'] ?? '';
$selectedType = $_GET['type'] ?? '';
$selectedSession = $_GET['session'] ?? '';

if (!$subjectId || !is_numeric($subjectId)) {
    redirect('index.php');
}

// جلب معلومات المادة والمستوى
$stmt = $pdo->prepare("
    SELECT s.*, l.name as level_name, l.id as level_id 
    FROM subjects s 
    JOIN levels l ON s.level_id = l.id 
    WHERE s.id = ?
");
$stmt->execute([$subjectId]);
$subject = $stmt->fetch();

if (!$subject || !isExamLevel($subject['level_id'])) {
    redirect('index.php');
}

// جلب السنوات المتاحة لهذه المادة
$yearsStmt = $pdo->prepare("
    SELECT DISTINCT d.exam_year 
    FROM documents d 
    WHERE d.subject_id = ? AND d.exam_year IS NOT NULL 
    ORDER BY d.exam_year DESC
");
$yearsStmt->execute([$subjectId]);
$availableYears = $yearsStmt->fetchAll(PDO::FETCH_COLUMN);

// جلب الوثائق حسب الفلاتر
$documents = getDocumentsByYearAndSubject($subjectId, $selectedYear, $selectedType, $selectedSession);

// جلب إحصائيات المادة
$statsStmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_documents,
        COUNT(CASE WHEN type = 'موضوع شهادة' THEN 1 END) as subjects_count,
        COUNT(CASE WHEN type = 'حل شهادة' THEN 1 END) as solutions_count,
        COUNT(DISTINCT exam_year) as years_count,
        SUM(download_count) as total_downloads
    FROM documents 
    WHERE subject_id = ? AND is_approved = 1
");
$statsStmt->execute([$subjectId]);
$stats = $statsStmt->fetch();

// تحديد نوع الشهادة
$examType = $subject['level_id'] == 4 ? 'متوسط' : 'بكالوريا';
$examName = $subject['level_id'] == 4 ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $subject['name'] ?> - <?= $examName ?> - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/enhancements.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .subject-header {
            background: linear-gradient(135deg, <?= $subject['color'] ?? '#3498db' ?>, <?= adjustBrightness($subject['color'] ?? '#3498db', -20) ?>);
            color: white;
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .subject-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }
        
        .subject-header .container {
            position: relative;
            z-index: 2;
        }
        
        .subject-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        .exam-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-block;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            backdrop-filter: blur(10px);
        }
        
        .filters-section {
            background: white;
            padding: 2rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 70px;
            z-index: 100;
        }
        
        .filters-container {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.9rem;
        }
        
        .filter-select {
            padding: 0.7rem 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            background: white;
            color: #2c3e50;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 150px;
        }
        
        .filter-select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }
        
        .filter-btn {
            padding: 0.7rem 1.5rem;
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1.5rem;
        }
        
        .filter-btn:hover {
            background: linear-gradient(135deg, #2980b9, #1f5f8b);
            transform: translateY(-2px);
        }
        
        .stats-bar {
            background: linear-gradient(135deg, #f8f9fa, #ffffff);
            padding: 2rem 0;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }
        
        .stat-item {
            background: white;
            padding: 1.5rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stat-item i {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: <?= $subject['color'] ?? '#3498db' ?>;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }
        
        .documents-section {
            padding: 3rem 0;
        }
        
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .document-card {
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .document-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: <?= $subject['color'] ?? '#3498db' ?>;
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .document-card:hover::before {
            transform: scaleX(1);
        }
        
        .document-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        
        .document-type {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: <?= $subject['color'] ?? '#3498db' ?>;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .document-year {
            position: absolute;
            top: 1rem;
            left: 1rem;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .document-session {
            display: inline-block;
            background: rgba(<?= $subject['color'] ?? '#3498db' ?>, 0.1);
            color: <?= $subject['color'] ?? '#3498db' ?>;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .no-documents {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }
        
        .no-documents i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 1rem;
        }
        
        @media (max-width: 768px) {
            .subject-header h1 {
                font-size: 2rem;
            }
            
            .filters-container {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                width: 100%;
            }
            
            .documents-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php include 'includes/header.php'; ?>

    <!-- Subject Header -->
    <section class="subject-header">
        <div class="container">
            <div class="exam-badge fade-in">
                <i class="fas fa-certificate"></i> <?= $examName ?>
            </div>
            <h1 class="slide-in-top"><?= $subject['name'] ?></h1>
            <p class="fade-in" style="animation-delay: 0.3s"><?= $subject['description'] ?></p>
        </div>
    </section>

    <!-- Filters Section -->
    <section class="filters-section">
        <div class="container">
            <form method="GET" action="" class="filters-container">
                <input type="hidden" name="id" value="<?= $subjectId ?>">
                
                <div class="filter-group">
                    <label for="year">السنة:</label>
                    <select name="year" id="year" class="filter-select">
                        <option value="">جميع السنوات</option>
                        <?php foreach($availableYears as $year): ?>
                            <option value="<?= $year ?>" <?= $selectedYear == $year ? 'selected' : '' ?>>
                                <?= $year ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="type">النوع:</label>
                    <select name="type" id="type" class="filter-select">
                        <option value="">جميع الأنواع</option>
                        <option value="موضوع شهادة" <?= $selectedType == 'موضوع شهادة' ? 'selected' : '' ?>>مواضيع الامتحان</option>
                        <option value="حل شهادة" <?= $selectedType == 'حل شهادة' ? 'selected' : '' ?>>الحلول النموذجية</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="session">الدورة:</label>
                    <select name="session" id="session" class="filter-select">
                        <option value="">جميع الدورات</option>
                        <option value="الدورة العادية" <?= $selectedSession == 'الدورة العادية' ? 'selected' : '' ?>>الدورة العادية</option>
                        <option value="دورة الاستدراك" <?= $selectedSession == 'دورة الاستدراك' ? 'selected' : '' ?>>دورة الاستدراك</option>
                        <option value="دورة استثنائية" <?= $selectedSession == 'دورة استثنائية' ? 'selected' : '' ?>>دورة استثنائية</option>
                    </select>
                </div>
                
                <button type="submit" class="filter-btn ripple-effect">
                    <i class="fas fa-filter"></i> تطبيق الفلتر
                </button>
            </form>
        </div>
    </section>

    <!-- Statistics Bar -->
    <section class="stats-bar">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item bounce-in" style="animation-delay: 0.1s">
                    <i class="fas fa-file-alt"></i>
                    <div class="stat-number"><?= $stats['subjects_count'] ?></div>
                    <div class="stat-label">موضوع امتحان</div>
                </div>
                <div class="stat-item bounce-in" style="animation-delay: 0.2s">
                    <i class="fas fa-check-circle"></i>
                    <div class="stat-number"><?= $stats['solutions_count'] ?></div>
                    <div class="stat-label">حل نموذجي</div>
                </div>
                <div class="stat-item bounce-in" style="animation-delay: 0.3s">
                    <i class="fas fa-calendar-alt"></i>
                    <div class="stat-number"><?= $stats['years_count'] ?></div>
                    <div class="stat-label">سنة متاحة</div>
                </div>
                <div class="stat-item bounce-in" style="animation-delay: 0.4s">
                    <i class="fas fa-download"></i>
                    <div class="stat-number"><?= $stats['total_downloads'] ?></div>
                    <div class="stat-label">تحميل</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Documents Section -->
    <section class="documents-section">
        <div class="container">
            <?php if($documents): ?>
                <div class="documents-grid">
                    <?php foreach($documents as $index => $doc): ?>
                        <div class="document-card slide-in-bottom interactive-element" 
                             style="animation-delay: <?= $index * 0.1 ?>s">
                            <?php if($doc['exam_year']): ?>
                                <div class="document-year"><?= $doc['exam_year'] ?></div>
                            <?php endif; ?>
                            
                            <div class="document-type">
                                <i class="fas fa-<?= $doc['type'] == 'موضوع شهادة' ? 'file-alt' : 'check-circle' ?>"></i>
                                <?= $doc['type'] ?>
                            </div>
                            
                            <?php if($doc['exam_session']): ?>
                                <div class="document-session"><?= $doc['exam_session'] ?></div>
                            <?php endif; ?>
                            
                            <h3><?= $doc['title'] ?></h3>
                            
                            <?php if($doc['description']): ?>
                                <p class="document-description"><?= substr($doc['description'], 0, 150) ?>...</p>
                            <?php endif; ?>
                            
                            <div class="document-meta">
                                <span><i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($doc['upload_date'])) ?></span>
                                <span><i class="fas fa-download"></i> <?= $doc['download_count'] ?> تحميل</span>
                            </div>
                            
                            <div class="document-actions">
                                <a href="view_document.php?id=<?= $doc['id'] ?>" class="btn btn-outline ripple-effect">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="download.php?id=<?= $doc['id'] ?>" class="btn btn-primary ripple-effect">
                                    <i class="fas fa-download"></i> تحميل
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php else: ?>
                <div class="no-documents fade-in">
                    <i class="fas fa-folder-open"></i>
                    <h3>لا توجد مواضيع متاحة</h3>
                    <p>لم يتم العثور على مواضيع تطابق معايير البحث المحددة.</p>
                    <a href="?id=<?= $subjectId ?>" class="btn btn-primary">عرض جميع المواضيع</a>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>

    <script src="assets/js/script.js"></script>
    <script src="assets/js/enhancements.js"></script>
</body>
</html>

<?php
// دالة مساعدة لتعديل سطوع اللون
function adjustBrightness($hex, $percent) {
    $hex = str_replace('#', '', $hex);
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));
    
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}
?>
