<?php
session_start();
require_once 'db/config.php';

// التحقق من وجود معرف الوثيقة
$documentId = $_GET['id'] ?? null;
if (!$documentId || !is_numeric($documentId)) {
    redirect('index.php');
}

// جلب معلومات الوثيقة
$stmt = $pdo->prepare("
    SELECT d.*, s.name as subject_name, s.color as subject_color, s.icon as subject_icon,
           l.name as level_name, l.id as level_id, u.full_name as uploader_name
    FROM documents d
    JOIN subjects s ON d.subject_id = s.id
    JOIN levels l ON s.level_id = l.id
    LEFT JOIN users u ON d.uploaded_by = u.id
    WHERE d.id = ? AND d.is_approved = 1
");
$stmt->execute([$documentId]);
$document = $stmt->fetch();

if (!$document) {
    redirect('index.php');
}

// زيادة عداد المشاهدات
$viewStmt = $pdo->prepare("UPDATE documents SET view_count = view_count + 1 WHERE id = ?");
$viewStmt->execute([$documentId]);

// جلب الوثائق ذات الصلة
$relatedStmt = $pdo->prepare("
    SELECT d.*, s.name as subject_name, s.color as subject_color
    FROM documents d
    JOIN subjects s ON d.subject_id = s.id
    WHERE d.subject_id = ? AND d.id != ? AND d.is_approved = 1
    ORDER BY d.upload_date DESC
    LIMIT 6
");
$relatedStmt->execute([$document['subject_id'], $documentId]);
$relatedDocuments = $relatedStmt->fetchAll();

// جلب التقييمات
$ratingsStmt = $pdo->prepare("
    SELECT AVG(rating) as avg_rating, COUNT(*) as total_ratings
    FROM ratings
    WHERE document_id = ?
");
$ratingsStmt->execute([$documentId]);
$ratingData = $ratingsStmt->fetch();

// جلب التعليقات
$commentsStmt = $pdo->prepare("
    SELECT c.*, u.full_name as user_name, u.avatar
    FROM comments c
    JOIN users u ON c.user_id = u.id
    WHERE c.document_id = ? AND c.is_approved = 1
    ORDER BY c.created_at DESC
    LIMIT 10
");
$commentsStmt->execute([$documentId]);
$comments = $commentsStmt->fetchAll();

// معالجة إضافة تعليق
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isLoggedIn()) {
    $content = trim($_POST['content'] ?? '');
    
    if ($content) {
        $insertComment = $pdo->prepare("
            INSERT INTO comments (document_id, user_id, content, created_at)
            VALUES (?, ?, ?, NOW())
        ");
        
        if ($insertComment->execute([$documentId, $_SESSION['user_id'], $content])) {
            setMessage('تم إضافة التعليق بنجاح', 'success');
            redirect("view_document.php?id=$documentId");
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($document['title']) ?> - منصة التعليم</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .document-header {
            background: linear-gradient(135deg, <?= $document['subject_color'] ?? '#3498db' ?>, <?= adjustBrightness($document['subject_color'] ?? '#3498db', -20) ?>);
            color: white;
            padding: 3rem 0;
        }
        
        .document-info {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 2rem;
            align-items: center;
        }
        
        .document-details h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .document-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 2rem;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .document-type-badge {
            background: rgba(255,255,255,0.2);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: bold;
        }
        
        .document-actions {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .btn-large {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 10px;
            text-align: center;
            min-width: 200px;
        }
        
        .document-content {
            padding: 3rem 0;
        }
        
        .content-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
        }
        
        .main-content {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .sidebar-content {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .info-card h3 {
            color: #2c3e50;
            margin-bottom: 1.5rem;
            font-size: 1.3rem;
        }
        
        .info-list {
            list-style: none;
            padding: 0;
        }
        
        .info-list li {
            display: flex;
            justify-content: space-between;
            padding: 0.7rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-list li:last-child {
            border-bottom: none;
        }
        
        .rating-display {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .stars {
            display: flex;
            gap: 0.2rem;
        }
        
        .star {
            color: #ddd;
            font-size: 1.2rem;
        }
        
        .star.filled {
            color: #f39c12;
        }
        
        .rating-text {
            color: #666;
            font-size: 0.9rem;
        }
        
        .pdf-viewer {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .comments-section {
            margin-top: 3rem;
        }
        
        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .comment-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        
        .comment-form textarea {
            width: 100%;
            min-height: 100px;
            padding: 1rem;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            resize: vertical;
            font-family: inherit;
        }
        
        .comment-form textarea:focus {
            outline: none;
            border-color: <?= $document['subject_color'] ?? '#3498db' ?>;
        }
        
        .comment {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .comment-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .comment-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: <?= $document['subject_color'] ?? '#3498db' ?>;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .comment-meta {
            flex: 1;
        }
        
        .comment-author {
            font-weight: bold;
            color: #2c3e50;
        }
        
        .comment-date {
            color: #666;
            font-size: 0.9rem;
        }
        
        .related-documents {
            margin-top: 3rem;
        }
        
        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
        }
        
        .related-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .related-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.15);
        }
        
        @media (max-width: 768px) {
            .document-info {
                grid-template-columns: 1fr;
                text-align: center;
            }
            
            .document-details h1 {
                font-size: 2rem;
            }
            
            .content-layout {
                grid-template-columns: 1fr;
            }
            
            .document-meta {
                justify-content: center;
            }
            
            .btn-large {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <h1><i class="fas fa-graduation-cap"></i> منصة التعليم</h1>
            </div>
            <nav class="nav-menu">
                <ul>
                    <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                    <li><a href="level.php?id=<?= $document['level_id'] ?>"><i class="fas fa-layer-group"></i> <?= $document['level_name'] ?></a></li>
                    <li><a href="subject.php?id=<?= $document['subject_id'] ?>"><i class="fas fa-book"></i> <?= $document['subject_name'] ?></a></li>
                    <li><a href="search.php"><i class="fas fa-search"></i> البحث</a></li>
                    <?php if(isLoggedIn()): ?>
                        <li><a href="profile.php"><i class="fas fa-user"></i> الملف الشخصي</a></li>
                        <?php if(isAdmin()): ?>
                            <li><a href="dashboard/index.php"><i class="fas fa-cog"></i> لوحة التحكم</a></li>
                        <?php endif; ?>
                        <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
                    <?php else: ?>
                        <li><a href="login.php"><i class="fas fa-sign-in-alt"></i> تسجيل الدخول</a></li>
                        <li><a href="register.php"><i class="fas fa-user-plus"></i> إنشاء حساب</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
            <div class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- Document Header -->
    <section class="document-header">
        <div class="container">
            <div class="document-info">
                <div class="document-details">
                    <div class="document-type-badge">
                        <i class="<?= $document['subject_icon'] ?? 'fas fa-book' ?>"></i>
                        <?= $document['type'] ?>
                    </div>
                    <h1><?= htmlspecialchars($document['title']) ?></h1>
                    <div class="document-meta">
                        <div class="meta-item">
                            <i class="fas fa-layer-group"></i>
                            <span><?= $document['level_name'] ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-book"></i>
                            <span><?= $document['subject_name'] ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span><?= $document['uploader_name'] ?? 'غير محدد' ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span><?= date('Y-m-d', strtotime($document['upload_date'])) ?></span>
                        </div>
                    </div>
                </div>
                
                <div class="document-actions">
                    <a href="download.php?id=<?= $document['id'] ?>" class="btn btn-primary btn-large">
                        <i class="fas fa-download"></i> تحميل الملف
                    </a>
                    <?php if(isLoggedIn()): ?>
                        <button onclick="toggleFavorite(<?= $document['id'] ?>)" class="btn btn-outline btn-large" id="favoriteBtn">
                            <i class="fas fa-heart"></i> إضافة للمفضلة
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Breadcrumb -->
    <section class="breadcrumb">
        <div class="container">
            <ul class="breadcrumb-list">
                <li><a href="index.php"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><i class="fas fa-chevron-left"></i></li>
                <li><a href="level.php?id=<?= $document['level_id'] ?>"><?= $document['level_name'] ?></a></li>
                <li><i class="fas fa-chevron-left"></i></li>
                <li><a href="subject.php?id=<?= $document['subject_id'] ?>"><?= $document['subject_name'] ?></a></li>
                <li><i class="fas fa-chevron-left"></i></li>
                <li><?= htmlspecialchars($document['title']) ?></li>
            </ul>
        </div>
    </section>

    <!-- Document Content -->
    <section class="document-content">
        <div class="container">
            <div class="content-layout">
                <div class="main-content">
                    <?php if($document['description']): ?>
                        <div class="document-description">
                            <h3><i class="fas fa-info-circle"></i> وصف الملف</h3>
                            <p><?= nl2br(htmlspecialchars($document['description'])) ?></p>
                        </div>
                        <hr style="margin: 2rem 0;">
                    <?php endif; ?>
                    
                    <!-- PDF Viewer for PDF files -->
                    <?php if($document['file_type'] === 'pdf'): ?>
                        <div class="pdf-preview">
                            <h3><i class="fas fa-file-pdf"></i> معاينة الملف</h3>
                            <iframe src="<?= $document['file_path'] ?>" class="pdf-viewer"></iframe>
                        </div>
                    <?php else: ?>
                        <div class="file-preview">
                            <h3><i class="fas fa-file"></i> معلومات الملف</h3>
                            <p>نوع الملف: <?= strtoupper($document['file_type']) ?></p>
                            <p>حجم الملف: <?= formatFileSize($document['file_size']) ?></p>
                            <p>لعرض الملف، يرجى تحميله أولاً.</p>
                        </div>
                    <?php endif; ?>

                    <!-- Comments Section -->
                    <div class="comments-section">
                        <div class="comments-header">
                            <h3><i class="fas fa-comments"></i> التعليقات (<?= count($comments) ?>)</h3>
                        </div>

                        <?php if(isLoggedIn()): ?>
                            <form method="POST" class="comment-form">
                                <h4>إضافة تعليق</h4>
                                <textarea name="content" placeholder="اكتب تعليقك هنا..." required></textarea>
                                <button type="submit" class="btn btn-primary" style="margin-top: 1rem;">
                                    <i class="fas fa-paper-plane"></i> إرسال التعليق
                                </button>
                            </form>
                        <?php else: ?>
                            <div class="comment-form">
                                <p>يجب <a href="login.php">تسجيل الدخول</a> لإضافة تعليق.</p>
                            </div>
                        <?php endif; ?>

                        <div class="comments-list">
                            <?php foreach($comments as $comment): ?>
                                <div class="comment">
                                    <div class="comment-header">
                                        <div class="comment-avatar">
                                            <?= strtoupper(substr($comment['user_name'], 0, 1)) ?>
                                        </div>
                                        <div class="comment-meta">
                                            <div class="comment-author"><?= htmlspecialchars($comment['user_name']) ?></div>
                                            <div class="comment-date"><?= date('Y-m-d H:i', strtotime($comment['created_at'])) ?></div>
                                        </div>
                                    </div>
                                    <div class="comment-content">
                                        <?= nl2br(htmlspecialchars($comment['content'])) ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="sidebar-content">
                    <!-- Document Info -->
                    <div class="info-card">
                        <h3><i class="fas fa-info-circle"></i> معلومات الملف</h3>
                        <ul class="info-list">
                            <li>
                                <span>نوع الملف:</span>
                                <span><?= strtoupper($document['file_type']) ?></span>
                            </li>
                            <li>
                                <span>حجم الملف:</span>
                                <span><?= formatFileSize($document['file_size']) ?></span>
                            </li>
                            <li>
                                <span>عدد التحميلات:</span>
                                <span><?= $document['download_count'] ?></span>
                            </li>
                            <li>
                                <span>عدد المشاهدات:</span>
                                <span><?= $document['view_count'] ?></span>
                            </li>
                            <li>
                                <span>تاريخ الرفع:</span>
                                <span><?= date('Y-m-d', strtotime($document['upload_date'])) ?></span>
                            </li>
                        </ul>
                    </div>

                    <!-- Rating -->
                    <div class="info-card">
                        <h3><i class="fas fa-star"></i> التقييم</h3>
                        <div class="rating-display">
                            <div class="stars">
                                <?php 
                                $avgRating = round($ratingData['avg_rating'] ?? 0);
                                for($i = 1; $i <= 5; $i++): 
                                ?>
                                    <i class="fas fa-star star <?= $i <= $avgRating ? 'filled' : '' ?>"></i>
                                <?php endfor; ?>
                            </div>
                            <div class="rating-text">
                                <?= number_format($ratingData['avg_rating'] ?? 0, 1) ?> 
                                (<?= $ratingData['total_ratings'] ?> تقييم)
                            </div>
                        </div>
                        <?php if(isLoggedIn()): ?>
                            <div class="rate-document">
                                <p>قيم هذا الملف:</p>
                                <div class="rating-stars" data-document-id="<?= $document['id'] ?>">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <i class="fas fa-star star-rate" data-rating="<?= $i ?>"></i>
                                    <?php endfor; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Share -->
                    <div class="info-card">
                        <h3><i class="fas fa-share-alt"></i> مشاركة</h3>
                        <div class="share-buttons">
                            <a href="#" onclick="shareOnFacebook()" class="btn btn-outline" style="margin-bottom: 0.5rem;">
                                <i class="fab fa-facebook"></i> فيسبوك
                            </a>
                            <a href="#" onclick="shareOnTwitter()" class="btn btn-outline" style="margin-bottom: 0.5rem;">
                                <i class="fab fa-twitter"></i> تويتر
                            </a>
                            <a href="#" onclick="copyLink()" class="btn btn-outline">
                                <i class="fas fa-link"></i> نسخ الرابط
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Documents -->
            <?php if($relatedDocuments): ?>
                <div class="related-documents">
                    <h2><i class="fas fa-file-alt"></i> ملفات ذات صلة</h2>
                    <div class="related-grid">
                        <?php foreach($relatedDocuments as $related): ?>
                            <div class="related-card">
                                <div class="document-type" style="background: <?= $related['subject_color'] ?>;">
                                    <i class="fas fa-<?= $related['type'] == 'درس' ? 'book' : ($related['type'] == 'امتحان' ? 'file-alt' : 'check-circle') ?>"></i>
                                    <?= $related['type'] ?>
                                </div>
                                <h4><?= htmlspecialchars($related['title']) ?></h4>
                                <p class="related-meta">
                                    <i class="fas fa-calendar"></i> <?= date('Y-m-d', strtotime($related['upload_date'])) ?>
                                </p>
                                <div class="related-actions">
                                    <a href="view_document.php?id=<?= $related['id'] ?>" class="btn btn-outline btn-sm">عرض</a>
                                    <a href="download.php?id=<?= $related['id'] ?>" class="btn btn-primary btn-sm">تحميل</a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4><i class="fas fa-graduation-cap"></i> منصة التعليم</h4>
                    <p>منصة تعليمية شاملة توفر دروس وامتحانات وحلول نموذجية لجميع المستويات التعليمية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="search.php">البحث</a></li>
                        <li><a href="contact.php">اتصل بنا</a></li>
                        <li><a href="about.php">من نحن</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 منصة التعليم. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/script.js"></script>
    <script>
        // تقييم الملف
        document.querySelectorAll('.star-rate').forEach(star => {
            star.addEventListener('click', function() {
                const rating = this.dataset.rating;
                const documentId = this.parentElement.dataset.documentId;
                
                fetch('ajax/rate_document.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        document_id: documentId,
                        rating: rating
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage('تم تقييم الملف بنجاح', 'success');
                        location.reload();
                    } else {
                        showMessage('حدث خطأ أثناء التقييم', 'error');
                    }
                });
            });
        });

        // مشاركة على فيسبوك
        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.href);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        // مشاركة على تويتر
        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('<?= htmlspecialchars($document['title']) ?>');
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
        }

        // نسخ الرابط
        function copyLink() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showMessage('تم نسخ الرابط', 'success');
            });
        }

        // إضافة/إزالة من المفضلة
        function toggleFavorite(documentId) {
            fetch('ajax/toggle_favorite.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    document_id: documentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const btn = document.getElementById('favoriteBtn');
                    if (data.is_favorite) {
                        btn.innerHTML = '<i class="fas fa-heart"></i> إزالة من المفضلة';
                        btn.classList.remove('btn-outline');
                        btn.classList.add('btn-primary');
                    } else {
                        btn.innerHTML = '<i class="fas fa-heart"></i> إضافة للمفضلة';
                        btn.classList.remove('btn-primary');
                        btn.classList.add('btn-outline');
                    }
                    showMessage(data.message, 'success');
                }
            });
        }
    </script>
</body>
</html>

<?php
// دالة مساعدة لتعديل سطوع اللون
function adjustBrightness($hex, $percent) {
    $hex = str_replace('#', '', $hex);
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    
    $r = max(0, min(255, $r + ($r * $percent / 100)));
    $g = max(0, min(255, $g + ($g * $percent / 100)));
    $b = max(0, min(255, $b + ($b * $percent / 100)));
    
    return sprintf("#%02x%02x%02x", $r, $g, $b);
}
?>
