<?php
session_start();
require_once '../db/config.php';

header('Content-Type: application/json');

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'يجب تسجيل الدخول أولاً']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$documentId = $input['document_id'] ?? null;
$rating = $input['rating'] ?? null;

if (!$documentId || !$rating || $rating < 1 || $rating > 5) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit;
}

try {
    // التحقق من وجود الوثيقة
    $docStmt = $pdo->prepare("SELECT id FROM documents WHERE id = ? AND is_approved = 1");
    $docStmt->execute([$documentId]);
    
    if (!$docStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الوثيقة غير موجودة']);
        exit;
    }
    
    // إدراج أو تحديث التقييم
    $stmt = $pdo->prepare("
        INSERT INTO ratings (document_id, user_id, rating, created_at) 
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE rating = ?, created_at = NOW()
    ");
    
    if ($stmt->execute([$documentId, $_SESSION['user_id'], $rating, $rating])) {
        // تسجيل النشاط
        logActivity($_SESSION['user_id'], 'rate_document', "تقييم الوثيقة $documentId بـ $rating نجوم");
        
        echo json_encode(['success' => true, 'message' => 'تم تقييم الملف بنجاح']);
    } else {
        echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء التقييم']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ في النظام']);
}
?>
