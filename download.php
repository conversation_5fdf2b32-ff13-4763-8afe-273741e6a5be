<?php
session_start();
require_once 'db/config.php';

// التحقق من وجود معرف الوثيقة
$documentId = $_GET['id'] ?? null;
if (!$documentId || !is_numeric($documentId)) {
    redirect('index.php');
}

// جلب معلومات الوثيقة
$stmt = $pdo->prepare("
    SELECT d.*, s.name as subject_name, l.name as level_name
    FROM documents d
    JOIN subjects s ON d.subject_id = s.id
    JOIN levels l ON s.level_id = l.id
    WHERE d.id = ? AND d.is_approved = 1
");
$stmt->execute([$documentId]);
$document = $stmt->fetch();

if (!$document) {
    setMessage('الملف غير موجود أو غير متاح', 'error');
    redirect('index.php');
}

// التحقق من وجود الملف على الخادم
$filePath = $document['file_path'];
if (!file_exists($filePath)) {
    setMessage('الملف غير موجود على الخادم', 'error');
    redirect("view_document.php?id=$documentId");
}

// زيادة عداد التحميلات
incrementDownloadCount($documentId);

// تسجيل عملية التحميل
if (isLoggedIn()) {
    logActivity($_SESSION['user_id'], 'download', "تحميل الملف: {$document['title']}");
}

// تحديد نوع المحتوى
$mimeTypes = [
    'pdf' => 'application/pdf',
    'doc' => 'application/msword',
    'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'ppt' => 'application/vnd.ms-powerpoint',
    'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'jpg' => 'image/jpeg',
    'jpeg' => 'image/jpeg',
    'png' => 'image/png',
    'gif' => 'image/gif',
    'txt' => 'text/plain',
    'zip' => 'application/zip',
    'rar' => 'application/x-rar-compressed'
];

$fileExtension = strtolower($document['file_type']);
$mimeType = $mimeTypes[$fileExtension] ?? 'application/octet-stream';

// تنظيف اسم الملف للتحميل
$downloadFileName = sanitizeFileName($document['title']) . '.' . $fileExtension;

// إعداد headers للتحميل
header('Content-Type: ' . $mimeType);
header('Content-Disposition: attachment; filename="' . $downloadFileName . '"');
header('Content-Length: ' . filesize($filePath));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// منع التخزين المؤقت
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');

// قراءة وإرسال الملف
if (readfile($filePath) === false) {
    setMessage('حدث خطأ أثناء تحميل الملف', 'error');
    redirect("view_document.php?id=$documentId");
}

exit;

/**
 * تنظيف اسم الملف لجعله آمناً للتحميل
 */
function sanitizeFileName($filename) {
    // إزالة الأحرف الخاصة والمسافات الزائدة
    $filename = trim($filename);
    
    // استبدال المسافات بشرطات سفلية
    $filename = str_replace(' ', '_', $filename);
    
    // إزالة الأحرف غير المرغوب فيها
    $filename = preg_replace('/[^a-zA-Z0-9\u0600-\u06FF_-]/', '', $filename);
    
    // تحديد الطول الأقصى
    if (strlen($filename) > 100) {
        $filename = substr($filename, 0, 100);
    }
    
    return $filename ?: 'document';
}
?>
