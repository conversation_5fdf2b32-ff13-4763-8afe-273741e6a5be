/* ملف التحسينات المتقدمة - Enhanced Animations & Effects */

/* تأثيرات الجسيمات المتحركة */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    pointer-events: none;
    animation: particleFloat 6s infinite ease-in-out;
}

@keyframes particleFloat {
    0%, 100% {
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100vh) translateX(50px) rotate(360deg);
        opacity: 0;
    }
}

/* تأثيرات الموجات */
.wave-effect {
    position: relative;
    overflow: hidden;
}

.wave-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: wave 2s infinite;
}

@keyframes wave {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* تأثيرات الضوء المتحرك */
.light-beam {
    position: relative;
    overflow: hidden;
}

.light-beam::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.light-beam:hover::after {
    opacity: 1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* تأثيرات الانفجار */
.explosion-effect {
    position: relative;
}

.explosion-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
}

.explosion-effect:active::before {
    width: 300px;
    height: 300px;
    opacity: 1;
}

/* تأثيرات الذبذبة */
.vibrate {
    animation: vibrate 0.3s linear infinite;
}

@keyframes vibrate {
    0% { transform: translate(0); }
    20% { transform: translate(-2px, 2px); }
    40% { transform: translate(-2px, -2px); }
    60% { transform: translate(2px, 2px); }
    80% { transform: translate(2px, -2px); }
    100% { transform: translate(0); }
}

/* تأثيرات الانزلاق المتقدمة */
.slide-in-top {
    animation: slideInTop 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes slideInTop {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-in-bottom {
    animation: slideInBottom 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes slideInBottom {
    0% {
        transform: translateY(100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

/* تأثيرات التكبير المتقدمة */
.zoom-in {
    animation: zoomIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes zoomIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: scale(1);
    }
}

/* تأثيرات الدوران المتقدمة */
.rotate-in {
    animation: rotateIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes rotateIn {
    0% {
        transform: rotate(-360deg) scale(0);
        opacity: 0;
    }
    100% {
        transform: rotate(0) scale(1);
        opacity: 1;
    }
}

/* تأثيرات الانقلاب */
.flip-in-x {
    animation: flipInX 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes flipInX {
    0% {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-10deg);
    }
    70% {
        transform: perspective(400px) rotateX(10deg);
    }
    100% {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

/* تأثيرات الارتداد */
.bounce-in {
    animation: bounceIn 1s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* تأثيرات الاهتزاز */
.shake {
    animation: shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

/* تأثيرات التموج */
.ripple-effect {
    position: relative;
    overflow: hidden;
    transform: translate3d(0, 0, 0);
}

.ripple-effect:after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
    background-repeat: no-repeat;
    background-position: 50%;
    transform: scale(10, 10);
    opacity: 0;
    transition: transform 0.5s, opacity 1s;
}

.ripple-effect:active:after {
    transform: scale(0, 0);
    opacity: 0.3;
    transition: 0s;
}

/* تأثيرات الضباب */
.blur-in {
    animation: blurIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes blurIn {
    0% {
        filter: blur(12px);
        opacity: 0;
    }
    100% {
        filter: blur(0px);
        opacity: 1;
    }
}

/* تأثيرات الانزلاق الجانبي */
.slide-in-elliptic-top {
    animation: slideInEllipticTop 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

@keyframes slideInEllipticTop {
    0% {
        transform: translateY(-600px) rotateX(-30deg) scale(0);
        transform-origin: 50% 100%;
        opacity: 0;
    }
    100% {
        transform: translateY(0) rotateX(0) scale(1);
        transform-origin: 50% 1400px;
        opacity: 1;
    }
}

/* تأثيرات الظهور المتدرج */
.fade-in-fwd {
    animation: fadeInFwd 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) both;
}

@keyframes fadeInFwd {
    0% {
        transform: translateZ(-80px);
        opacity: 0;
    }
    100% {
        transform: translateZ(0);
        opacity: 1;
    }
}
